{"version": 3, "sources": ["../../../src/build/static-paths/app.ts"], "sourcesContent": ["import type { ParamValue, Params } from '../../server/request/params'\nimport type { AppPageModule } from '../../server/route-modules/app-page/module'\nimport type { AppSegment } from '../segment-config/app/app-segments'\nimport type { StaticPathsResult } from './types'\n\nimport path from 'path'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport { createWorkStore } from '../../server/async-storage/work-store'\nimport { FallbackMode } from '../../lib/fallback'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport {\n  getRouteRegex,\n  type RouteRegex,\n} from '../../shared/lib/router/utils/route-regex'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { normalizePathname, encodeParam } from './utils'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { createIncrementalCache } from '../../export/helpers/create-incremental-cache'\nimport type { NextConfigComplete } from '../../server/config-shared'\n\n/**\n * Compares two parameters to see if they're equal.\n *\n * @param a - The first parameter.\n * @param b - The second parameter.\n * @returns Whether the parameters are equal.\n */\nfunction areParamValuesEqual(a: ParamValue, b: ParamValue) {\n  // If they're equal, then we can return true.\n  if (a === b) {\n    return true\n  }\n\n  // If they're both arrays, then we can return true if they have the same\n  // length and all the items are the same.\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) {\n      return false\n    }\n\n    return a.every((item, index) => item === b[index])\n  }\n\n  // Otherwise, they're not equal.\n  return false\n}\n\n/**\n * Filters out duplicate parameters from a list of parameters.\n *\n * @param routeParamKeys - The keys of the parameters.\n * @param routeParams - The list of parameters to filter.\n * @returns The list of unique parameters.\n */\nfunction filterUniqueParams(\n  routeParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const unique: Params[] = []\n\n  for (const params of routeParams) {\n    let i = 0\n    for (; i < unique.length; i++) {\n      const item = unique[i]\n      let j = 0\n      for (; j < routeParamKeys.length; j++) {\n        const key = routeParamKeys[j]\n\n        // If the param is not the same, then we need to break out of the loop.\n        if (!areParamValuesEqual(item[key], params[key])) {\n          break\n        }\n      }\n\n      // If we got to the end of the paramKeys array, then it means that we\n      // found a duplicate. Skip it.\n      if (j === routeParamKeys.length) {\n        break\n      }\n    }\n\n    // If we didn't get to the end of the unique array, then it means that we\n    // found a duplicate. Skip it.\n    if (i < unique.length) {\n      continue\n    }\n\n    unique.push(params)\n  }\n\n  return unique\n}\n\n/**\n * Filters out all combinations of root params from a list of parameters.\n *\n * Given the following root param ('lang'), and the following routeParams:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US', slug: ['home'] },\n *   { lang: 'en', region: 'US', slug: ['about'] },\n *   { lang: 'fr', region: 'CA', slug: ['about'] },\n * ]\n * ```\n *\n * The result will be:\n *\n * ```\n * [\n *   { lang: 'en', region: 'US' },\n *   { lang: 'fr', region: 'CA' },\n * ]\n * ```\n *\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to filter.\n * @returns The list of combinations of root params.\n */\nfunction filterRootParamsCombinations(\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const combinations: Params[] = []\n\n  for (const params of routeParams) {\n    const combination: Params = {}\n\n    // Collect all root params. As soon as we don't find a root param, break.\n    let i = 0\n    for (; i < rootParamKeys.length; i++) {\n      const key = rootParamKeys[i]\n      if (params[key]) {\n        combination[key] = params[key]\n      } else {\n        break\n      }\n    }\n\n    // If we didn't find all root params, skip this combination. We only want to\n    // generate combinations that have all root params.\n    if (i < rootParamKeys.length) {\n      continue\n    }\n\n    combinations.push(combination)\n  }\n\n  return combinations\n}\n\n/**\n * Validates the parameters to ensure they're accessible and have the correct\n * types.\n *\n * @param page - The page to validate.\n * @param regex - The route regex.\n * @param isRoutePPREnabled - Whether the route has partial prerendering enabled.\n * @param routeParamKeys - The keys of the parameters.\n * @param rootParamKeys - The keys of the root params.\n * @param routeParams - The list of parameters to validate.\n * @returns The list of validated parameters.\n */\nfunction validateParams(\n  page: string,\n  regex: RouteRegex,\n  isRoutePPREnabled: boolean,\n  routeParamKeys: readonly string[],\n  rootParamKeys: readonly string[],\n  routeParams: readonly Params[]\n): Params[] {\n  const valid: Params[] = []\n\n  // Validate that if there are any root params, that the user has provided at\n  // least one value for them only if we're using partial prerendering.\n  if (isRoutePPREnabled && rootParamKeys.length > 0) {\n    if (\n      routeParams.length === 0 ||\n      rootParamKeys.some((key) =>\n        routeParams.some((params) => !(key in params))\n      )\n    ) {\n      if (rootParamKeys.length === 1) {\n        throw new Error(\n          `A required root parameter (${rootParamKeys[0]}) was not provided in generateStaticParams for ${page}, please provide at least one value.`\n        )\n      }\n\n      throw new Error(\n        `Required root params (${rootParamKeys.join(', ')}) were not provided in generateStaticParams for ${page}, please provide at least one value for each.`\n      )\n    }\n  }\n\n  for (const params of routeParams) {\n    const item: Params = {}\n\n    for (const key of routeParamKeys) {\n      const { repeat, optional } = regex.groups[key]\n\n      let paramValue = params[key]\n\n      if (\n        optional &&\n        params.hasOwnProperty(key) &&\n        (paramValue === null ||\n          paramValue === undefined ||\n          (paramValue as any) === false)\n      ) {\n        paramValue = []\n      }\n\n      // A parameter is missing, so the rest of the params are not accessible.\n      // We only support this when the route has partial prerendering enabled.\n      // This will make it so that the remaining params are marked as missing so\n      // we can generate a fallback route for them.\n      if (!paramValue && isRoutePPREnabled) {\n        break\n      }\n\n      // Perform validation for the parameter based on whether it's a repeat\n      // parameter or not.\n      if (repeat) {\n        if (!Array.isArray(paramValue)) {\n          throw new Error(\n            `A required parameter (${key}) was not provided as an array received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      } else {\n        if (typeof paramValue !== 'string') {\n          throw new Error(\n            `A required parameter (${key}) was not provided as a string received ${typeof paramValue} in generateStaticParams for ${page}`\n          )\n        }\n      }\n\n      item[key] = paramValue\n    }\n\n    valid.push(item)\n  }\n\n  return valid\n}\n\n/**\n * Builds the static paths for an app using `generateStaticParams`.\n *\n * @param params - The parameters for the build.\n * @returns The static paths.\n */\nexport async function buildAppStaticPaths({\n  dir,\n  page,\n  distDir,\n  dynamicIO,\n  authInterrupts,\n  segments,\n  isrFlushToDisk,\n  cacheHandler,\n  cacheLifeProfiles,\n  requestHeaders,\n  cacheHandlers,\n  maxMemoryCacheSize,\n  fetchCacheKeyPrefix,\n  nextConfigOutput,\n  ComponentMod,\n  isRoutePPREnabled = false,\n  buildId,\n  rootParamKeys,\n}: {\n  dir: string\n  page: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  segments: AppSegment[]\n  distDir: string\n  isrFlushToDisk?: boolean\n  fetchCacheKeyPrefix?: string\n  cacheHandler?: string\n  cacheHandlers?: NextConfigComplete['experimental']['cacheHandlers']\n  cacheLifeProfiles?: {\n    [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n  }\n  maxMemoryCacheSize?: number\n  requestHeaders: IncrementalCache['requestHeaders']\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  ComponentMod: AppPageModule\n  isRoutePPREnabled: boolean\n  buildId: string\n  rootParamKeys: readonly string[]\n}): Promise<Partial<StaticPathsResult>> {\n  if (\n    segments.some((generate) => generate.config?.dynamicParams === true) &&\n    nextConfigOutput === 'export'\n  ) {\n    throw new Error(\n      '\"dynamicParams: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/app/building-your-application/deploying/static-exports'\n    )\n  }\n\n  ComponentMod.patchFetch()\n\n  const incrementalCache = await createIncrementalCache({\n    dir,\n    distDir,\n    cacheHandler,\n    cacheHandlers,\n    requestHeaders,\n    fetchCacheKeyPrefix,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const regex = getRouteRegex(page)\n  const routeParamKeys = Object.keys(getRouteMatcher(regex)(page) || {})\n\n  const afterRunner = new AfterRunner()\n\n  const store = createWorkStore({\n    page,\n    // We're discovering the parameters here, so we don't have any unknown\n    // ones.\n    fallbackRouteParams: null,\n    renderOpts: {\n      incrementalCache,\n      cacheLifeProfiles,\n      supportsDynamicResponse: true,\n      isRevalidate: false,\n      experimental: {\n        dynamicIO,\n        authInterrupts,\n      },\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n    },\n    buildId,\n  })\n\n  const routeParams = await ComponentMod.workAsyncStorage.run(\n    store,\n    async () => {\n      async function builtRouteParams(\n        parentsParams: Params[] = [],\n        idx = 0\n      ): Promise<Params[]> {\n        // If we don't have any more to process, then we're done.\n        if (idx === segments.length) return parentsParams\n\n        const current = segments[idx]\n\n        if (\n          typeof current.generateStaticParams !== 'function' &&\n          idx < segments.length\n        ) {\n          return builtRouteParams(parentsParams, idx + 1)\n        }\n\n        const params: Params[] = []\n\n        if (current.generateStaticParams) {\n          // fetchCache can be used to inform the fetch() defaults used inside\n          // of generateStaticParams. revalidate and dynamic options don't come into\n          // play within generateStaticParams.\n          if (typeof current.config?.fetchCache !== 'undefined') {\n            store.fetchCache = current.config.fetchCache\n          }\n\n          if (parentsParams.length > 0) {\n            for (const parentParams of parentsParams) {\n              const result = await current.generateStaticParams({\n                params: parentParams,\n              })\n\n              for (const item of result) {\n                params.push({ ...parentParams, ...item })\n              }\n            }\n          } else {\n            const result = await current.generateStaticParams({ params: {} })\n\n            params.push(...result)\n          }\n        }\n\n        if (idx < segments.length) {\n          return builtRouteParams(params, idx + 1)\n        }\n\n        return params\n      }\n\n      return builtRouteParams()\n    }\n  )\n\n  let lastDynamicSegmentHadGenerateStaticParams = false\n  for (const segment of segments) {\n    // Check to see if there are any missing params for segments that have\n    // dynamicParams set to false.\n    if (\n      segment.param &&\n      segment.isDynamicSegment &&\n      segment.config?.dynamicParams === false\n    ) {\n      for (const params of routeParams) {\n        if (segment.param in params) continue\n\n        const relative = segment.filePath\n          ? path.relative(dir, segment.filePath)\n          : undefined\n\n        throw new Error(\n          `Segment \"${relative}\" exports \"dynamicParams: false\" but the param \"${segment.param}\" is missing from the generated route params.`\n        )\n      }\n    }\n\n    if (\n      segment.isDynamicSegment &&\n      typeof segment.generateStaticParams !== 'function'\n    ) {\n      lastDynamicSegmentHadGenerateStaticParams = false\n    } else if (typeof segment.generateStaticParams === 'function') {\n      lastDynamicSegmentHadGenerateStaticParams = true\n    }\n  }\n\n  // Determine if all the segments have had their parameters provided.\n  const hadAllParamsGenerated =\n    routeParamKeys.length === 0 ||\n    (routeParams.length > 0 &&\n      routeParams.every((params) => {\n        for (const key of routeParamKeys) {\n          if (key in params) continue\n          return false\n        }\n        return true\n      }))\n\n  // TODO: dynamic params should be allowed to be granular per segment but\n  // we need additional information stored/leveraged in the prerender\n  // manifest to allow this behavior.\n  const dynamicParams = segments.every(\n    (segment) => segment.config?.dynamicParams !== false\n  )\n\n  const supportsRoutePreGeneration =\n    hadAllParamsGenerated || process.env.NODE_ENV === 'production'\n\n  const fallbackMode = dynamicParams\n    ? supportsRoutePreGeneration\n      ? isRoutePPREnabled\n        ? FallbackMode.PRERENDER\n        : FallbackMode.BLOCKING_STATIC_RENDER\n      : undefined\n    : FallbackMode.NOT_FOUND\n\n  const result: Partial<StaticPathsResult> = {\n    fallbackMode,\n    prerenderedRoutes: lastDynamicSegmentHadGenerateStaticParams\n      ? []\n      : undefined,\n  }\n\n  if (hadAllParamsGenerated || isRoutePPREnabled) {\n    if (isRoutePPREnabled) {\n      // Discover all unique combinations of the rootParams so we can generate\n      // shells for each of them if they're available.\n      routeParams.unshift(\n        ...filterRootParamsCombinations(rootParamKeys, routeParams)\n      )\n\n      result.prerenderedRoutes ??= []\n      result.prerenderedRoutes.push({\n        pathname: page,\n        encodedPathname: page,\n        fallbackRouteParams: routeParamKeys,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            rootParamKeys.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams: rootParamKeys,\n      })\n    }\n\n    filterUniqueParams(\n      routeParamKeys,\n      validateParams(\n        page,\n        regex,\n        isRoutePPREnabled,\n        routeParamKeys,\n        rootParamKeys,\n        routeParams\n      )\n    ).forEach((params) => {\n      let pathname: string = page\n      let encodedPathname: string = page\n\n      const fallbackRouteParams: string[] = []\n\n      for (const key of routeParamKeys) {\n        if (fallbackRouteParams.length > 0) {\n          // This is a partial route, so we should add the value to the\n          // fallbackRouteParams.\n          fallbackRouteParams.push(key)\n          continue\n        }\n\n        let paramValue = params[key]\n\n        if (!paramValue) {\n          if (isRoutePPREnabled) {\n            // This is a partial route, so we should add the value to the\n            // fallbackRouteParams.\n            fallbackRouteParams.push(key)\n            continue\n          } else {\n            // This route is not complete, and we aren't performing a partial\n            // prerender, so we should return, skipping this route.\n            return\n          }\n        }\n\n        const { repeat, optional } = regex.groups[key]\n        let replaced = `[${repeat ? '...' : ''}${key}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n\n        pathname = pathname.replace(\n          replaced,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n        encodedPathname = encodedPathname.replace(\n          replaced,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      }\n\n      const fallbackRootParams = rootParamKeys.filter((param) =>\n        fallbackRouteParams.includes(param)\n      )\n\n      result.prerenderedRoutes ??= []\n      result.prerenderedRoutes.push({\n        pathname: normalizePathname(pathname),\n        encodedPathname: normalizePathname(encodedPathname),\n        fallbackRouteParams,\n        fallbackMode: dynamicParams\n          ? // If the fallback params includes any root params, then we need to\n            // perform a blocking static render.\n            fallbackRootParams.length > 0\n            ? FallbackMode.BLOCKING_STATIC_RENDER\n            : fallbackMode\n          : FallbackMode.NOT_FOUND,\n        fallbackRootParams,\n      })\n    })\n  }\n\n  await afterRunner.executeAfter()\n\n  return result\n}\n"], "names": ["buildAppStaticPaths", "areParamValuesEqual", "a", "b", "Array", "isArray", "length", "every", "item", "index", "filterUniqueParams", "routeParamKeys", "routeParams", "unique", "params", "i", "j", "key", "push", "filterRootParamsCombinations", "rootParamKeys", "combinations", "combination", "validateParams", "page", "regex", "isRoutePPREnabled", "valid", "some", "Error", "join", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "undefined", "dir", "distDir", "dynamicIO", "authInterrupts", "segments", "isrFlushToDisk", "cache<PERSON><PERSON><PERSON>", "cacheLifeProfiles", "requestHeaders", "cacheHandlers", "maxMemoryCacheSize", "fetchCacheKeyPrefix", "nextConfigOutput", "ComponentMod", "buildId", "generate", "config", "dynamicParams", "patchFetch", "incrementalCache", "createIncrementalCache", "flushToDisk", "cacheMaxMemorySize", "getRouteRegex", "Object", "keys", "getRouteMatcher", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "store", "createWorkStore", "fallbackRouteParams", "renderOpts", "supportsDynamicResponse", "isRevalidate", "experimental", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "workAsyncStorage", "run", "builtRouteParams", "parents<PERSON><PERSON><PERSON>", "idx", "current", "generateStaticParams", "fetchCache", "parentParams", "result", "lastDynamicSegmentHadGenerateStaticParams", "segment", "param", "isDynamicSegment", "relative", "filePath", "path", "hadAllParamsGenerated", "supportsRoutePreGeneration", "process", "env", "NODE_ENV", "fallbackMode", "FallbackMode", "PRERENDER", "BLOCKING_STATIC_RENDER", "NOT_FOUND", "prerenderedRoutes", "unshift", "pathname", "encodedPathname", "fallbackRootParams", "for<PERSON>ach", "replaced", "replace", "encodeParam", "value", "escapePathDelimiters", "encodeURIComponent", "filter", "includes", "normalizePathname", "executeAfter"], "mappings": ";;;;+BA2PsBA;;;eAAAA;;;6DAtPL;8BACW;2BACI;0BACH;8BACG;4BAIzB;uBAEwC;6EACd;wCACM;;;;;;AAGvC;;;;;;CAMC,GACD,SAASC,oBAAoBC,CAAa,EAAEC,CAAa;IACvD,6CAA6C;IAC7C,IAAID,MAAMC,GAAG;QACX,OAAO;IACT;IAEA,wEAAwE;IACxE,yCAAyC;IACzC,IAAIC,MAAMC,OAAO,CAACH,MAAME,MAAMC,OAAO,CAACF,IAAI;QACxC,IAAID,EAAEI,MAAM,KAAKH,EAAEG,MAAM,EAAE;YACzB,OAAO;QACT;QAEA,OAAOJ,EAAEK,KAAK,CAAC,CAACC,MAAMC,QAAUD,SAASL,CAAC,CAACM,MAAM;IACnD;IAEA,gCAAgC;IAChC,OAAO;AACT;AAEA;;;;;;CAMC,GACD,SAASC,mBACPC,cAAiC,EACjCC,WAA8B;IAE9B,MAAMC,SAAmB,EAAE;IAE3B,KAAK,MAAMC,UAAUF,YAAa;QAChC,IAAIG,IAAI;QACR,MAAOA,IAAIF,OAAOP,MAAM,EAAES,IAAK;YAC7B,MAAMP,OAAOK,MAAM,CAACE,EAAE;YACtB,IAAIC,IAAI;YACR,MAAOA,IAAIL,eAAeL,MAAM,EAAEU,IAAK;gBACrC,MAAMC,MAAMN,cAAc,CAACK,EAAE;gBAE7B,uEAAuE;gBACvE,IAAI,CAACf,oBAAoBO,IAAI,CAACS,IAAI,EAAEH,MAAM,CAACG,IAAI,GAAG;oBAChD;gBACF;YACF;YAEA,qEAAqE;YACrE,8BAA8B;YAC9B,IAAID,MAAML,eAAeL,MAAM,EAAE;gBAC/B;YACF;QACF;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,IAAIS,IAAIF,OAAOP,MAAM,EAAE;YACrB;QACF;QAEAO,OAAOK,IAAI,CAACJ;IACd;IAEA,OAAOD;AACT;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,SAASM,6BACPC,aAAgC,EAChCR,WAA8B;IAE9B,MAAMS,eAAyB,EAAE;IAEjC,KAAK,MAAMP,UAAUF,YAAa;QAChC,MAAMU,cAAsB,CAAC;QAE7B,yEAAyE;QACzE,IAAIP,IAAI;QACR,MAAOA,IAAIK,cAAcd,MAAM,EAAES,IAAK;YACpC,MAAME,MAAMG,aAAa,CAACL,EAAE;YAC5B,IAAID,MAAM,CAACG,IAAI,EAAE;gBACfK,WAAW,CAACL,IAAI,GAAGH,MAAM,CAACG,IAAI;YAChC,OAAO;gBACL;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QACnD,IAAIF,IAAIK,cAAcd,MAAM,EAAE;YAC5B;QACF;QAEAe,aAAaH,IAAI,CAACI;IACpB;IAEA,OAAOD;AACT;AAEA;;;;;;;;;;;CAWC,GACD,SAASE,eACPC,IAAY,EACZC,KAAiB,EACjBC,iBAA0B,EAC1Bf,cAAiC,EACjCS,aAAgC,EAChCR,WAA8B;IAE9B,MAAMe,QAAkB,EAAE;IAE1B,4EAA4E;IAC5E,qEAAqE;IACrE,IAAID,qBAAqBN,cAAcd,MAAM,GAAG,GAAG;QACjD,IACEM,YAAYN,MAAM,KAAK,KACvBc,cAAcQ,IAAI,CAAC,CAACX,MAClBL,YAAYgB,IAAI,CAAC,CAACd,SAAW,CAAEG,CAAAA,OAAOH,MAAK,KAE7C;YACA,IAAIM,cAAcd,MAAM,KAAK,GAAG;gBAC9B,MAAM,qBAEL,CAFK,IAAIuB,MACR,CAAC,2BAA2B,EAAET,aAAa,CAAC,EAAE,CAAC,+CAA+C,EAAEI,KAAK,oCAAoC,CAAC,GADtI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,sBAAsB,EAAET,cAAcU,IAAI,CAAC,MAAM,gDAAgD,EAAEN,KAAK,6CAA6C,CAAC,GADnJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,KAAK,MAAMV,UAAUF,YAAa;QAChC,MAAMJ,OAAe,CAAC;QAEtB,KAAK,MAAMS,OAAON,eAAgB;YAChC,MAAM,EAAEoB,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChB,IAAI;YAE9C,IAAIiB,aAAapB,MAAM,CAACG,IAAI;YAE5B,IACEe,YACAlB,OAAOqB,cAAc,CAAClB,QACrBiB,CAAAA,eAAe,QACdA,eAAeE,aACf,AAACF,eAAuB,KAAI,GAC9B;gBACAA,aAAa,EAAE;YACjB;YAEA,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,6CAA6C;YAC7C,IAAI,CAACA,cAAcR,mBAAmB;gBACpC;YACF;YAEA,sEAAsE;YACtE,oBAAoB;YACpB,IAAIK,QAAQ;gBACV,IAAI,CAAC3B,MAAMC,OAAO,CAAC6B,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEZ,IAAI,wCAAwC,EAAE,OAAOiB,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF,OAAO;gBACL,IAAI,OAAOU,eAAe,UAAU;oBAClC,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,sBAAsB,EAAEZ,IAAI,wCAAwC,EAAE,OAAOiB,WAAW,6BAA6B,EAAEV,MAAM,GAD1H,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEAhB,IAAI,CAACS,IAAI,GAAGiB;QACd;QAEAP,MAAMT,IAAI,CAACV;IACb;IAEA,OAAOmB;AACT;AAQO,eAAe3B,oBAAoB,EACxCqC,GAAG,EACHb,IAAI,EACJc,OAAO,EACPC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,kBAAkB,EAClBC,mBAAmB,EACnBC,gBAAgB,EAChBC,YAAY,EACZxB,oBAAoB,KAAK,EACzByB,OAAO,EACP/B,aAAa,EAsBd;IACC,IACEqB,SAASb,IAAI,CAAC,CAACwB;YAAaA;eAAAA,EAAAA,mBAAAA,SAASC,MAAM,qBAAfD,iBAAiBE,aAAa,MAAK;UAC/DL,qBAAqB,UACrB;QACA,MAAM,qBAEL,CAFK,IAAIpB,MACR,mKADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAqB,aAAaK,UAAU;IAEvB,MAAMC,mBAAmB,MAAMC,IAAAA,8CAAsB,EAAC;QACpDpB;QACAC;QACAK;QACAG;QACAD;QACAG;QACAU,aAAahB;QACbiB,oBAAoBZ;IACtB;IAEA,MAAMtB,QAAQmC,IAAAA,yBAAa,EAACpC;IAC5B,MAAMb,iBAAiBkD,OAAOC,IAAI,CAACC,IAAAA,6BAAe,EAACtC,OAAOD,SAAS,CAAC;IAEpE,MAAMwC,cAAc,IAAIC,yBAAW;IAEnC,MAAMC,QAAQC,IAAAA,0BAAe,EAAC;QAC5B3C;QACA,sEAAsE;QACtE,QAAQ;QACR4C,qBAAqB;QACrBC,YAAY;YACVb;YACAZ;YACA0B,yBAAyB;YACzBC,cAAc;YACdC,cAAc;gBACZjC;gBACAC;YACF;YACAiC,WAAWT,YAAYU,OAAO,CAACD,SAAS;YACxCE,SAASX,YAAYU,OAAO,CAACC,OAAO;YACpCC,kBAAkBZ,YAAYU,OAAO,CAACG,WAAW;QACnD;QACA1B;IACF;IAEA,MAAMvC,cAAc,MAAMsC,aAAa4B,gBAAgB,CAACC,GAAG,CACzDb,OACA;QACE,eAAec,iBACbC,gBAA0B,EAAE,EAC5BC,MAAM,CAAC;YAEP,yDAAyD;YACzD,IAAIA,QAAQzC,SAASnC,MAAM,EAAE,OAAO2E;YAEpC,MAAME,UAAU1C,QAAQ,CAACyC,IAAI;YAE7B,IACE,OAAOC,QAAQC,oBAAoB,KAAK,cACxCF,MAAMzC,SAASnC,MAAM,EACrB;gBACA,OAAO0E,iBAAiBC,eAAeC,MAAM;YAC/C;YAEA,MAAMpE,SAAmB,EAAE;YAE3B,IAAIqE,QAAQC,oBAAoB,EAAE;oBAIrBD;gBAHX,oEAAoE;gBACpE,0EAA0E;gBAC1E,oCAAoC;gBACpC,IAAI,SAAOA,kBAAAA,QAAQ9B,MAAM,qBAAd8B,gBAAgBE,UAAU,MAAK,aAAa;oBACrDnB,MAAMmB,UAAU,GAAGF,QAAQ9B,MAAM,CAACgC,UAAU;gBAC9C;gBAEA,IAAIJ,cAAc3E,MAAM,GAAG,GAAG;oBAC5B,KAAK,MAAMgF,gBAAgBL,cAAe;wBACxC,MAAMM,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;4BAChDtE,QAAQwE;wBACV;wBAEA,KAAK,MAAM9E,QAAQ+E,OAAQ;4BACzBzE,OAAOI,IAAI,CAAC;gCAAE,GAAGoE,YAAY;gCAAE,GAAG9E,IAAI;4BAAC;wBACzC;oBACF;gBACF,OAAO;oBACL,MAAM+E,SAAS,MAAMJ,QAAQC,oBAAoB,CAAC;wBAAEtE,QAAQ,CAAC;oBAAE;oBAE/DA,OAAOI,IAAI,IAAIqE;gBACjB;YACF;YAEA,IAAIL,MAAMzC,SAASnC,MAAM,EAAE;gBACzB,OAAO0E,iBAAiBlE,QAAQoE,MAAM;YACxC;YAEA,OAAOpE;QACT;QAEA,OAAOkE;IACT;IAGF,IAAIQ,4CAA4C;IAChD,KAAK,MAAMC,WAAWhD,SAAU;YAM5BgD;QALF,sEAAsE;QACtE,8BAA8B;QAC9B,IACEA,QAAQC,KAAK,IACbD,QAAQE,gBAAgB,IACxBF,EAAAA,kBAAAA,QAAQpC,MAAM,qBAAdoC,gBAAgBnC,aAAa,MAAK,OAClC;YACA,KAAK,MAAMxC,UAAUF,YAAa;gBAChC,IAAI6E,QAAQC,KAAK,IAAI5E,QAAQ;gBAE7B,MAAM8E,WAAWH,QAAQI,QAAQ,GAC7BC,aAAI,CAACF,QAAQ,CAACvD,KAAKoD,QAAQI,QAAQ,IACnCzD;gBAEJ,MAAM,qBAEL,CAFK,IAAIP,MACR,CAAC,SAAS,EAAE+D,SAAS,gDAAgD,EAAEH,QAAQC,KAAK,CAAC,6CAA6C,CAAC,GAD/H,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IACED,QAAQE,gBAAgB,IACxB,OAAOF,QAAQL,oBAAoB,KAAK,YACxC;YACAI,4CAA4C;QAC9C,OAAO,IAAI,OAAOC,QAAQL,oBAAoB,KAAK,YAAY;YAC7DI,4CAA4C;QAC9C;IACF;IAEA,oEAAoE;IACpE,MAAMO,wBACJpF,eAAeL,MAAM,KAAK,KACzBM,YAAYN,MAAM,GAAG,KACpBM,YAAYL,KAAK,CAAC,CAACO;QACjB,KAAK,MAAMG,OAAON,eAAgB;YAChC,IAAIM,OAAOH,QAAQ;YACnB,OAAO;QACT;QACA,OAAO;IACT;IAEJ,wEAAwE;IACxE,mEAAmE;IACnE,mCAAmC;IACnC,MAAMwC,gBAAgBb,SAASlC,KAAK,CAClC,CAACkF;YAAYA;eAAAA,EAAAA,kBAAAA,QAAQpC,MAAM,qBAAdoC,gBAAgBnC,aAAa,MAAK;;IAGjD,MAAM0C,6BACJD,yBAAyBE,QAAQC,GAAG,CAACC,QAAQ,KAAK;IAEpD,MAAMC,eAAe9C,gBACjB0C,6BACEtE,oBACE2E,sBAAY,CAACC,SAAS,GACtBD,sBAAY,CAACE,sBAAsB,GACrCnE,YACFiE,sBAAY,CAACG,SAAS;IAE1B,MAAMjB,SAAqC;QACzCa;QACAK,mBAAmBjB,4CACf,EAAE,GACFpD;IACN;IAEA,IAAI2D,yBAAyBrE,mBAAmB;QAC9C,IAAIA,mBAAmB;YACrB,wEAAwE;YACxE,gDAAgD;YAChDd,YAAY8F,OAAO,IACdvF,6BAA6BC,eAAeR;YAGjD2E,OAAOkB,iBAAiB,KAAK,EAAE;YAC/BlB,OAAOkB,iBAAiB,CAACvF,IAAI,CAAC;gBAC5ByF,UAAUnF;gBACVoF,iBAAiBpF;gBACjB4C,qBAAqBzD;gBACrByF,cAAc9C,gBAEV,oCAAoC;gBACpClC,cAAcd,MAAM,GAAG,IACrB+F,sBAAY,CAACE,sBAAsB,GACnCH,eACFC,sBAAY,CAACG,SAAS;gBAC1BK,oBAAoBzF;YACtB;QACF;QAEAV,mBACEC,gBACAY,eACEC,MACAC,OACAC,mBACAf,gBACAS,eACAR,cAEFkG,OAAO,CAAC,CAAChG;YACT,IAAI6F,WAAmBnF;YACvB,IAAIoF,kBAA0BpF;YAE9B,MAAM4C,sBAAgC,EAAE;YAExC,KAAK,MAAMnD,OAAON,eAAgB;gBAChC,IAAIyD,oBAAoB9D,MAAM,GAAG,GAAG;oBAClC,6DAA6D;oBAC7D,uBAAuB;oBACvB8D,oBAAoBlD,IAAI,CAACD;oBACzB;gBACF;gBAEA,IAAIiB,aAAapB,MAAM,CAACG,IAAI;gBAE5B,IAAI,CAACiB,YAAY;oBACf,IAAIR,mBAAmB;wBACrB,6DAA6D;wBAC7D,uBAAuB;wBACvB0C,oBAAoBlD,IAAI,CAACD;wBACzB;oBACF,OAAO;wBACL,iEAAiE;wBACjE,uDAAuD;wBACvD;oBACF;gBACF;gBAEA,MAAM,EAAEc,MAAM,EAAEC,QAAQ,EAAE,GAAGP,MAAMQ,MAAM,CAAChB,IAAI;gBAC9C,IAAI8F,WAAW,CAAC,CAAC,EAAEhF,SAAS,QAAQ,KAAKd,IAAI,CAAC,CAAC;gBAC/C,IAAIe,UAAU;oBACZ+E,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBAEAJ,WAAWA,SAASK,OAAO,CACzBD,UACAE,IAAAA,kBAAW,EAAC/E,YAAY,CAACgF,QAAUC,IAAAA,6BAAoB,EAACD,OAAO;gBAEjEN,kBAAkBA,gBAAgBI,OAAO,CACvCD,UACAE,IAAAA,kBAAW,EAAC/E,YAAYkF;YAE5B;YAEA,MAAMP,qBAAqBzF,cAAciG,MAAM,CAAC,CAAC3B,QAC/CtB,oBAAoBkD,QAAQ,CAAC5B;YAG/BH,OAAOkB,iBAAiB,KAAK,EAAE;YAC/BlB,OAAOkB,iBAAiB,CAACvF,IAAI,CAAC;gBAC5ByF,UAAUY,IAAAA,wBAAiB,EAACZ;gBAC5BC,iBAAiBW,IAAAA,wBAAiB,EAACX;gBACnCxC;gBACAgC,cAAc9C,gBAEV,oCAAoC;gBACpCuD,mBAAmBvG,MAAM,GAAG,IAC1B+F,sBAAY,CAACE,sBAAsB,GACnCH,eACFC,sBAAY,CAACG,SAAS;gBAC1BK;YACF;QACF;IACF;IAEA,MAAM7C,YAAYwD,YAAY;IAE9B,OAAOjC;AACT"}