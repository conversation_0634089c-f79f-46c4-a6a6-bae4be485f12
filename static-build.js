const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 构建静态网站
console.log('开始构建静态网站...');
try {
  execSync('pnpm build', { stdio: 'inherit' });
  console.log('静态网站构建完成！');
} catch (error) {
  console.error('构建失败:', error);
  process.exit(1);
}

// 检查out目录是否存在
if (!fs.existsSync(path.join(__dirname, 'out'))) {
  console.error('构建失败：未找到out目录');
  process.exit(1);
}

// 提示用户如何使用
console.log('\n✅ 静态文件已生成在out目录中');
console.log('您可以：');
console.log('1. 打开out目录，然后双击index.html文件查看网站');
console.log('2. 将out目录上传到任何静态网站托管服务（如GitHub Pages, Netlify等）');
console.log('3. 运行 node static-serve.js 在本地预览网站');

// 创建一个简单的本地预览服务器脚本
const serveScript = `
const http = require('http');
const fs = require('fs');
const path = require('path');

// 简单的MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 解析URL获取文件路径
  let filePath = path.join(__dirname, 'out', req.url === '/' ? 'index.html' : req.url);
  
  // 如果路径没有扩展名，假设是目录，尝试查找index.html
  if (!path.extname(filePath)) {
    filePath = path.join(filePath, 'index.html');
  }

  // 获取文件扩展名
  const extname = String(path.extname(filePath)).toLowerCase();
  const contentType = mimeTypes[extname] || 'application/octet-stream';

  // 读取文件
  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在
        res.writeHead(404);
        res.end('404 Not Found: ' + req.url);
      } else {
        // 服务器错误
        res.writeHead(500);
        res.end('500 Server Error: ' + error.code);
      }
    } else {
      // 成功响应
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

// 启动服务器
server.listen(3000, () => {
  console.log('预览服务器已启动，请访问 http://localhost:3000');
  console.log('按Ctrl+C可以停止服务器');
});
`;

// 写入服务器脚本
fs.writeFileSync(path.join(__dirname, 'static-serve.js'), serveScript);
console.log('\n✅ 已创建static-serve.js脚本，您可以使用它在本地预览网站');

// 更新package.json添加静态构建命令
try {
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJson.scripts = packageJson.scripts || {};
    packageJson.scripts['static'] = 'node static-build.js';
    packageJson.scripts['preview'] = 'node static-serve.js';
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('\n✅ 已添加新命令到package.json:');
    console.log('- pnpm static: 构建静态网站');
    console.log('- pnpm preview: 在本地预览静态网站');
  } else {
    console.log('\n未找到package.json文件，跳过添加命令');
  }
} catch (error) {
  console.error('更新package.json失败:', error);
}

console.log('\n🎉 全部完成！您的静态网站已准备就绪！'); 