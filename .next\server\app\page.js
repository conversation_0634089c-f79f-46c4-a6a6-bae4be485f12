/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3ebb074a9e19\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkY6XFxEZXNrdG9wXFzmnKzlnLDpobnnm65cXHNhYy1hbnRpY2hlYXRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzZWJiMDc0YTllMTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var geist_font_sans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! geist/font/sans */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1_/node_modules/geist/dist/sans.js\");\n/* harmony import */ var geist_font_mono__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! geist/font/mono */ \"(rsc)/./node_modules/.pnpm/geist@1.4.2_next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1_/node_modules/geist/dist/mono.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: 'SimpleAntiCheat (SAC)',\n    description: 'SimpleAntiCheat (SAC)',\n    generator: 'kldhsh.top'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    children: `\nhtml {\n  font-family: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.style.fontFamily};\n  --font-sans: ${geist_font_sans__WEBPACK_IMPORTED_MODULE_1__.GeistSans.variable};\n  --font-mono: ${geist_font_mono__WEBPACK_IMPORTED_MODULE_2__.GeistMono.variable};\n}\n        `\n                }, void 0, false, {\n                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUMyQztBQUNBO0FBQ3JCO0FBRWYsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxXQUFXO0FBQ2IsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7MEJBQ0MsNEVBQUNDOzhCQUFPLENBQUM7O2VBRUYsRUFBRVgsc0RBQVNBLENBQUNXLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO2VBQzdCLEVBQUVaLHNEQUFTQSxDQUFDYSxRQUFRLENBQUM7ZUFDckIsRUFBRVosc0RBQVNBLENBQUNZLFFBQVEsQ0FBQzs7UUFFNUIsQ0FBQzs7Ozs7Ozs7Ozs7MEJBRUgsOERBQUNDOzBCQUFNUDs7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIkY6XFxEZXNrdG9wXFzmnKzlnLDpobnnm65cXHNhYy1hbnRpY2hlYXRcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEdlaXN0U2FucyB9IGZyb20gJ2dlaXN0L2ZvbnQvc2FucydcbmltcG9ydCB7IEdlaXN0TW9ubyB9IGZyb20gJ2dlaXN0L2ZvbnQvbW9ubydcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdTaW1wbGVBbnRpQ2hlYXQgKFNBQyknLFxuICBkZXNjcmlwdGlvbjogJ1NpbXBsZUFudGlDaGVhdCAoU0FDKScsXG4gIGdlbmVyYXRvcjogJ2tsZGhzaC50b3AnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPHN0eWxlPntgXG5odG1sIHtcbiAgZm9udC1mYW1pbHk6ICR7R2Vpc3RTYW5zLnN0eWxlLmZvbnRGYW1pbHl9O1xuICAtLWZvbnQtc2FuczogJHtHZWlzdFNhbnMudmFyaWFibGV9O1xuICAtLWZvbnQtbW9ubzogJHtHZWlzdE1vbm8udmFyaWFibGV9O1xufVxuICAgICAgICBgfTwvc3R5bGU+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkdlaXN0U2FucyIsIkdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImdlbmVyYXRvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsInN0eWxlIiwiZm9udEZhbWlseSIsInZhcmlhYmxlIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"F:\\Desktop\\本地项目\\sac-anticheat\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?df05\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNEZXNrdG9wJTVDJTVDJUU2JTlDJUFDJUU1JTlDJUIwJUU5JUExJUI5JUU3JTlCJUFFJTVDJTVDc2FjLWFudGljaGVhdCU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBb0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXERlc2t0b3BcXFxc5pys5Zyw6aG555uuXFxcXHNhYy1hbnRpY2hlYXRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleAntiCheatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wallpaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/door-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/cable-car.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/footprints.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/message-square-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction SimpleAntiCheatPage() {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('zh');\n    const content = {\n        zh: {\n            title: \"SimpleAntiCheat (SAC)\",\n            subtitle: \"SCP: Secret Laboratory 服务端反作弊插件\",\n            developer: \"开发者\",\n            features: \"反作弊功能\",\n            community: \"社区支持\",\n            appeals: \"误封申诉\",\n            description: \"高效反作弊系统，为您的 SCP:SL 服务器提供全面保护。\",\n            performanceNote: \"性能影响几乎为零，保持服务器流畅运行\",\n            warningTitle: \"重要提醒\",\n            warningText: \"误判无法完全避免，我们正在持续改进算法。如遇误封请及时联系申诉。\",\n            contactInfo: \"联系方式\",\n            appealContact: \"申诉联系\",\n            qqGroup: \"QQ群\",\n            discord: \"Discord\",\n            githubProfile: \"GitHub 主页\",\n            featuresData: [\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    name: \"静默自瞄\",\n                    desc: \"检测并阻止自动瞄准作弊\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    name: \"无后坐力\",\n                    desc: \"防止武器后坐力修改\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    name: \"穿墙\",\n                    desc: \"检测非法穿墙行为\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-939 漏洞\",\n                    desc: \"修复 SCP-939 相关漏洞利用\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-106 漏洞\",\n                    desc: \"防止 SCP-106 漏洞滥用\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-096 漏洞\",\n                    desc: \"阻止 SCP-096 异常行为\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    name: \"移动解锁\",\n                    desc: \"检测非法移动速度修改\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    name: \"电网无伤\",\n                    desc: \"防止电网伤害绕过\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    name: \"远程交互门\",\n                    desc: \"阻止远距离门操作\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    name: \"远程交互电梯\",\n                    desc: \"防止远程电梯控制\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"远程交互914\",\n                    desc: \"限制 SCP-914 远程操作\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Bigstep (大跨步)\",\n                    desc: \"检测异常步长移动\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"BHop (连跳)\",\n                    desc: \"防止连续跳跃作弊\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"假物品\",\n                    desc: \"反物品透视保护\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"反敏感词\",\n                    desc: \"过滤不当聊天内容\"\n                }\n            ]\n        },\n        en: {\n            title: \"SimpleAntiCheat (SAC)\",\n            subtitle: \"Anti-Cheat Plugin for SCP: Secret Laboratory Servers\",\n            developer: \"Developer\",\n            features: \"Anti-Cheat Features\",\n            community: \"Community Support\",\n            appeals: \"False Positive Appeals\",\n            description: \"An efficient anti-cheat system providing comprehensive protection for your SCP:SL servers.\",\n            performanceNote: \"Near-zero performance impact, keeping your server running smoothly\",\n            warningTitle: \"Important Notice\",\n            warningText: \"False positives cannot be completely avoided. We are continuously improving our algorithms. Please contact us promptly if you encounter a false ban.\",\n            contactInfo: \"Contact Information\",\n            appealContact: \"Appeal Contact\",\n            qqGroup: \"QQ Group\",\n            discord: \"Discord\",\n            githubProfile: \"GitHub Profile\",\n            featuresData: [\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    name: \"Silent Aimbot\",\n                    desc: \"Detect and prevent auto-aim cheats\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    name: \"No Recoil\",\n                    desc: \"Prevent weapon recoil modifications\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    name: \"Wallhack\",\n                    desc: \"Detect illegal wall penetration\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-939 Exploit\",\n                    desc: \"Fix SCP-939 related exploits\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-106 Exploit\",\n                    desc: \"Prevent SCP-106 exploit abuse\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-096 Exploit\",\n                    desc: \"Block SCP-096 abnormal behaviors\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    name: \"Movement Unlock\",\n                    desc: \"Detect illegal movement speed modifications\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    name: \"Tesla Gate Bypass\",\n                    desc: \"Prevent tesla gate damage bypass\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    name: \"Remote Door Interaction\",\n                    desc: \"Block long-distance door operations\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    name: \"Remote Elevator Interaction\",\n                    desc: \"Prevent remote elevator control\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"Remote 914 Interaction\",\n                    desc: \"Limit SCP-914 remote operations\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Bigstep\",\n                    desc: \"Detect abnormal step length movement\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"BHop (Bunny Hop)\",\n                    desc: \"Prevent continuous jump cheats\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"Fake Items\",\n                    desc: \"Anti-item ESP protection\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"Anti-Profanity\",\n                    desc: \"Filter inappropriate chat content\"\n                }\n            ]\n        }\n    };\n    const t = content[language];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-[url('/abstract-digital-grid.png')] opacity-10\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gradient-to-t from-black/50 to-transparent\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-blue-500/20 bg-black/20 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://cdn.kldhsh.top/scpslgame/202508/8db91754516830.png\",\n                                                alt: \"SAC Logo\",\n                                                className: \"w-12 h-12 rounded-lg object-cover\",\n                                                onError: (e)=>{\n                                                    // 如果图片加载失败，显示Shield图标作为后备\n                                                    e.currentTarget.style.display = 'none';\n                                                    e.currentTarget.nextElementSibling.style.display = 'flex';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: t.title\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: t.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: language === 'zh' ? 'default' : 'outline',\n                                                size: \"sm\",\n                                                onClick: ()=>setLanguage('zh'),\n                                                className: \"text-xs\",\n                                                children: \"中文\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: language === 'en' ? 'default' : 'outline',\n                                                size: \"sm\",\n                                                onClick: ()=>setLanguage('en'),\n                                                className: \"text-xs\",\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                        children: t.title\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                        children: t.description\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 text-green-400 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: t.performanceNote\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                        className: \"max-w-2xl mx-auto bg-yellow-500/10 border-yellow-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                className: \"text-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t.warningTitle,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    t.warningText\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                defaultValue: \"features\",\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 bg-black/20 border border-blue-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"features\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.features\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"community\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.community\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"appeals\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.appeals\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"features\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: t.featuresData.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            className: \"pb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                            className: \"w-5 h-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                        className: \"text-white text-lg\",\n                                                                        children: feature.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                                className: \"text-gray-300\",\n                                                                children: feature.desc\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"community\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-white flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-5 h-5 mr-2 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t.qqGroup\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 mb-4\",\n                                                                    children: language === 'zh' ? '加入我们的QQ群获取支持和交流' : 'Join our QQ group for support and discussion'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-lg px-4 py-2 bg-green-500/20 text-green-300 border-green-500/20\",\n                                                                    children: \"1007642619\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-white flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-5 h-5 mr-2 text-purple-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discord\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 mb-4\",\n                                                                    children: language === 'zh' ? '加入Discord服务器进行国际交流' : 'Join our Discord server for international communication'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    asChild: true,\n                                                                    variant: \"outline\",\n                                                                    className: \"border-purple-500/20 text-purple-300 hover:bg-purple-500/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"https://discord.gg/PWRXKE6aNM\",\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        children: [\n                                                                            language === 'zh' ? '加入Discord' : 'Join Discord',\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"w-4 h-4 ml-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 238,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"appeals\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-red-500/10 border-red-500/20 max-w-2xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-white flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5 mr-2 text-red-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                t.appeals\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                            className: \"text-red-200\",\n                                                            children: language === 'zh' ? '如果您认为被误封，请通过以下方式联系我们' : 'If you believe you were falsely banned, please contact us through the following methods'\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/20 p-4 rounded-lg border border-red-500/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-semibold mb-2\",\n                                                                    children: t.appealContact\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-blue-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: \"QQ: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"bg-blue-500/20 text-blue-300 border-blue-500/20\",\n                                                                                    children: \"3298837494\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 267,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 272,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: [\n                                                                                        t.qqGroup,\n                                                                                        \": \"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"bg-green-500/20 text-green-300 border-green-500/20\",\n                                                                                    children: \"1007642619\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-purple-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: \"Discord: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    asChild: true,\n                                                                                    variant: \"link\",\n                                                                                    className: \"p-0 h-auto text-purple-300 hover:text-purple-200\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                        href: \"https://discord.gg/PWRXKE6aNM\",\n                                                                                        target: \"_blank\",\n                                                                                        rel: \"noopener noreferrer\",\n                                                                                        children: \"PWRXKE6aNM\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                                            className: \"bg-yellow-500/10 border-yellow-500/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                                    className: \"text-yellow-200\",\n                                                                    children: language === 'zh' ? '请提供详细的游戏信息和时间，以便我们快速处理您的申诉。' : 'Please provide detailed game information and timestamps for us to process your appeal quickly.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"border-t border-blue-500/20 bg-black/20 backdrop-blur-sm py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://cdn.kldhsh.top/scpslgame/202508/8db91754516830.png\",\n                                            alt: \"SAC Logo\",\n                                            className: \"w-6 h-6 rounded-lg object-cover\",\n                                            onError: (e)=>{\n                                                // 如果图片加载失败，显示Shield图标作为后备\n                                                e.currentTarget.style.display = 'none';\n                                                e.currentTarget.nextElementSibling.style.display = 'flex';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold\",\n                                            children: \"SimpleAntiCheat (SAC)\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: language === 'zh' ? '© 2025 Web Developed by kldhsh123。' : '© 2025 Web Developed by kldhsh123.'\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\", {\n    variants: {\n        variant: {\n            default: \"bg-card text-card-foreground\",\n            destructive: \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Alert({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert\",\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\nfunction AlertDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"alert-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.9_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n            destructive: \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"span\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.9_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tabs.tsx":
/*!********************************!*\
  !*** ./components/ui/tabs.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-tabs@1.1.2_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@19.1.9_r_x2bxvdeiol6kmae2kxvoli3hta/node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nfunction Tabs({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"tabs\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col gap-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction TabsList({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        \"data-slot\": \"tabs-list\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\nfunction TabsTrigger({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"tabs-trigger\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\nfunction TabsContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        \"data-slot\": \"tabs-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 outline-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\components\\\\ui\\\\tabs.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJGOlxcRGVza3RvcFxc5pys5Zyw6aG555uuXFxzYWMtYW50aWNoZWF0XFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4yLjRfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNEZXNrdG9wJTVDJTVDJUU2JTlDJUFDJUU1JTlDJUIwJUU5JUExJUI5JUU3JTlCJUFFJTVDJTVDc2FjLWFudGljaGVhdCU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SUFBb0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXERlc2t0b3BcXFxc5pys5Zyw6aG555uuXFxcXHNhYy1hbnRpY2hlYXRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Csans.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-sans%2FGeist-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22node_modules%5C%5C%5C%5C.pnpm%5C%5C%5C%5Cgeist%401.4.2_next%4015.2.4_react-dom%4019.1.1_react%4019.1.1__react%4019.1.1_%5C%5C%5C%5Cnode_modules%5C%5C%5C%5Cgeist%5C%5C%5C%5Cdist%5C%5C%5C%5Cmono.js%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2Fgeist-mono%2FGeistMono-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22ui-monospace%5C%22%2C%5C%22SFMono-Regular%5C%22%2C%5C%22Roboto%20Mono%5C%22%2C%5C%22Menlo%5C%22%2C%5C%22Monaco%5C%22%2C%5C%22Liberation%20Mono%5C%22%2C%5C%22DejaVu%20Sans%20Mono%5C%22%2C%5C%22Courier%20New%5C%22%2C%5C%22monospace%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22GeistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CDesktop%5C%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5C%5Csac-anticheat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1","vendor-chunks/lucide-react@0.454.0_react@19.1.1","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/geist@1.4.2_next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1_","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/clsx@2.1.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-tabs@1.1.2_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@19.1.9_r_x2bxvdeiol6kmae2kxvoli3hta","vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@_6bjm6z4wf6kdhgpxvfm4nsnfvi","vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@19._kllkkdus57j5qioedaaaxdprau","vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@19.1_3vioxb5ygpbiz2wo7smg6ylxlu","vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@19.1.9_react@19.1.1","vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.7_@types+react@19.1.9__@types+react@19_gwuomzhjf7ovz2cxuuuas5fc3m","vendor-chunks/@radix-ui+primitive@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CDesktop%5C%E6%9C%AC%E5%9C%B0%E9%A1%B9%E7%9B%AE%5Csac-anticheat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();