(()=>{var e={};e.id=974,e.ids=[974],e.modules={824:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2205,23)),Promise.resolve().then(r.t.bind(r,3757,23)),Promise.resolve().then(r.t.bind(r,1861,23)),Promise.resolve().then(r.t.bind(r,4260,23)),Promise.resolve().then(r.t.bind(r,2508,23)),Promise.resolve().then(r.t.bind(r,9792,23)),Promise.resolve().then(r.t.bind(r,526,23)),Promise.resolve().then(r.t.bind(r,1480,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2879:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4376:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3031,23)),Promise.resolve().then(r.t.bind(r,7019,23)),Promise.resolve().then(r.t.bind(r,8335,23)),Promise.resolve().then(r.t.bind(r,6926,23)),Promise.resolve().then(r.t.bind(r,698,23)),Promise.resolve().then(r.t.bind(r,1182,23)),Promise.resolve().then(r.t.bind(r,5020,23)),Promise.resolve().then(r.t.bind(r,7646,23))},4861:(e,t,r)=>{Promise.resolve().then(r.bind(r,4934))},4934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tA});var o=r(256),n=r(113),a=r.t(n,2);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,o=e.map(e=>{let o=s(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():s(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}var c=n.forwardRef((e,t)=>{let{children:r,...a}=e,s=n.Children.toArray(r),i=s.find(p);if(i){let e=i.props.children,r=s.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(d,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(d,{...a,ref:t,children:r})});c.displayName="Slot";var d=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return n.cloneElement(r,{...function(e,t){let r={...t};for(let o in t){let n=e[o],a=t[o];/^on[A-Z]/.test(o)?n&&a?r[o]=(...e)=>{a(...e),n(...e)}:n&&(r[o]=n):"style"===o?r[o]={...n,...a}:"className"===o&&(r[o]=[n,a].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props),ref:t?i(t,e):e})}return n.Children.count(r)>1?n.Children.only(null):null});d.displayName="SlotClone";var u=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function p(e){return n.isValidElement(e)&&e.type===u}function m(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o)}return n}(e))&&(o&&(o+=" "),o+=t);return o}let f=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,h=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return m(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:a}=t,s=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],o=null==a?void 0:a[e];if(null===t)return null;let s=f(t)||f(o);return n[e][s]}),i=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return m(e,s,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...i}[t]):({...a,...i})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)},b=e=>{let t=y(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),g(r,t)||v(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},g=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),n=o?g(e.slice(1),o):void 0;if(n)return n;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},x=/^\[(.+)\]$/,v=e=>{if(x.test(e)){let t=x.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},y=e=>{let{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return N(Object.entries(e.classGroups),r).forEach(([e,r])=>{w(r,o,e,t)}),o},w=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:k(t,e)).classGroupId=r;return}if("function"==typeof e){if(j(e)){w(e(o),t,r,o);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,n])=>{w(n,k(t,e),r,o)})})},k=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},j=e=>e.isThemeGetter,N=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,C=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,a)=>{r.set(n,a),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},M=e=>{let{separator:t,experimentalParseClassName:r}=e,o=1===t.length,n=t[0],a=t.length,s=e=>{let r;let s=[],i=0,l=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===i){if(d===n&&(o||e.slice(c,c+a)===t)){s.push(e.slice(l,c)),l=c+a;continue}if("/"===d){r=c;continue}}"["===d?i++:"]"===d&&i--}let c=0===s.length?e:e.substring(l),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:s,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:s}):s},P=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},z=e=>({cache:C(e.cacheSize),parseClassName:M(e),...b(e)}),S=/\s+/,A=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n}=t,a=[],s=e.trim().split(S),i="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),p=!!u,m=o(p?d.substring(0,u):d);if(!m){if(!p||!(m=o(d))){i=t+(i.length>0?" "+i:i);continue}p=!1}let f=P(l).join(":"),h=c?f+"!":f,b=h+m;if(a.includes(b))continue;a.push(b);let g=n(m,p);for(let e=0;e<g.length;++e){let t=g[e];a.push(h+t)}i=t+(i.length>0?" "+i:i)}return i};function E(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=R(e))&&(o&&(o+=" "),o+=t);return o}let R=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=R(e[o]))&&(r&&(r+=" "),r+=t);return r},D=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},I=/^\[(?:([a-z-]+):)?(.+)\]$/i,T=/^\d+\/\d+$/,_=new Set(["px","full","screen"]),F=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,O=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,L=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,G=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,$=e=>W(e)||_.has(e)||T.test(e),V=e=>eo(e,"length",en),W=e=>!!e&&!Number.isNaN(Number(e)),U=e=>eo(e,"number",W),B=e=>!!e&&Number.isInteger(Number(e)),K=e=>e.endsWith("%")&&W(e.slice(0,-1)),H=e=>I.test(e),Q=e=>F.test(e),Z=new Set(["length","size","percentage"]),X=e=>eo(e,Z,ea),J=e=>eo(e,"position",ea),Y=new Set(["image","url"]),ee=e=>eo(e,Y,ei),et=e=>eo(e,"",es),er=()=>!0,eo=(e,t,r)=>{let o=I.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},en=e=>O.test(e)&&!q.test(e),ea=()=>!1,es=e=>L.test(e),ei=e=>G.test(e);Symbol.toStringTag;let el=function(e,...t){let r,o,n;let a=function(i){return o=(r=z(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,a=s,s(i)};function s(e){let t=o(e);if(t)return t;let a=A(e,r);return n(e,a),a}return function(){return a(E.apply(null,arguments))}}(()=>{let e=D("colors"),t=D("spacing"),r=D("blur"),o=D("brightness"),n=D("borderColor"),a=D("borderRadius"),s=D("borderSpacing"),i=D("borderWidth"),l=D("contrast"),c=D("grayscale"),d=D("hueRotate"),u=D("invert"),p=D("gap"),m=D("gradientColorStops"),f=D("gradientColorStopPositions"),h=D("inset"),b=D("margin"),g=D("opacity"),x=D("padding"),v=D("saturate"),y=D("scale"),w=D("sepia"),k=D("skew"),j=D("space"),N=D("translate"),C=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",H,t],z=()=>[H,t],S=()=>["",$,V],A=()=>["auto",W,H],E=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",H],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],O=()=>[W,H];return{cacheSize:500,separator:":",theme:{colors:[er],spacing:[$,V],blur:["none","",Q,H],brightness:O(),borderColor:[e],borderRadius:["none","","full",Q,H],borderSpacing:z(),borderWidth:S(),contrast:O(),grayscale:_(),hueRotate:O(),invert:_(),gap:z(),gradientColorStops:[e],gradientColorStopPositions:[K,V],inset:P(),margin:P(),opacity:O(),padding:z(),saturate:O(),scale:O(),sepia:_(),skew:O(),space:z(),translate:z()},classGroups:{aspect:[{aspect:["auto","square","video",H]}],container:["container"],columns:[{columns:[Q]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...E(),H]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",B,H]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",H]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",B,H]}],"grid-cols":[{"grid-cols":[er]}],"col-start-end":[{col:["auto",{span:["full",B,H]},H]}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":[er]}],"row-start-end":[{row:["auto",{span:[B,H]},H]}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",H]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",H]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[x]}],px:[{px:[x]}],py:[{py:[x]}],ps:[{ps:[x]}],pe:[{pe:[x]}],pt:[{pt:[x]}],pr:[{pr:[x]}],pb:[{pb:[x]}],pl:[{pl:[x]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",H,t]}],"min-w":[{"min-w":[H,t,"min","max","fit"]}],"max-w":[{"max-w":[H,t,"none","full","min","max","fit","prose",{screen:[Q]},Q]}],h:[{h:[H,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[H,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[H,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Q,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",U]}],"font-family":[{font:[er]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",H]}],"line-clamp":[{"line-clamp":["none",W,U]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",$,H]}],"list-image":[{"list-image":["none",H]}],"list-style-type":[{list:["none","disc","decimal",H]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",$,V]}],"underline-offset":[{"underline-offset":["auto",$,H]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...E(),J]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",X]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ee]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:R()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[$,H]}],"outline-w":[{outline:[$,V]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:S()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[$,V]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Q,et]}],"shadow-color":[{shadow:[er]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...I(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":I()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Q,H]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",H]}],duration:[{duration:O()}],ease:[{ease:["linear","in","out","in-out",H]}],delay:[{delay:O()}],animate:[{animate:["none","spin","ping","pulse","bounce",H]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[B,H]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",H]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[$,V,U]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function ec(...e){return el(m(e))}let ed=h("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eu({className:e,variant:t,size:r,asChild:n=!1,...a}){let s=n?c:"button";return(0,o.jsx)(s,{"data-slot":"button",className:ec(ed({variant:t,size:r,className:e})),...a})}function ep({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card",className:ec("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function em({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-header",className:ec("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function ef({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-title",className:ec("leading-none font-semibold",e),...t})}function eh({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-description",className:ec("text-muted-foreground text-sm",e),...t})}function eb({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"card-content",className:ec("px-6",e),...t})}let eg=h("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function ex({className:e,variant:t,asChild:r=!1,...n}){let a=r?c:"span";return(0,o.jsx)(a,{"data-slot":"badge",className:ec(eg({variant:t}),e),...n})}function ev(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e?.(o),!1===r||!o.defaultPrevented)return t?.(o)}}function ey(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let s=n.createContext(a),i=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,c=r?.[e]?.[i]||s,d=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:d,children:a})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[i]||s,c=n.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:o})=>{let n=r(e)[`__scope${o}`];return{...t,...n}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}var ew=globalThis?.document?n.useLayoutEffect:()=>{},ek=a["useId".toString()]||(()=>void 0),ej=0;function eN(e){let[t,r]=n.useState(ek());return ew(()=>{e||r(e=>e??String(ej++))},[e]),e||(t?`radix-${t}`:"")}r(804);var eC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...a}=e,s=n?c:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...a,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function eM(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}function eP({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,a=n.useRef(o),s=eM(t);return n.useEffect(()=>{a.current!==o&&(s(o),a.current=o)},[o,a,s]),r}({defaultProp:t,onChange:r}),s=void 0!==e,i=s?e:o,l=eM(r);return[i,n.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else a(t)},[s,e,a,l])]}var ez=n.createContext(void 0);function eS(e){let t=n.useContext(ez);return e||t||"ltr"}var eA="rovingFocusGroup.onEntryFocus",eE={bubbles:!1,cancelable:!0},eR="RovingFocusGroup",[eD,eI,eT]=function(e){let t=e+"CollectionProvider",[r,a]=ey(t),[s,i]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,a=n.useRef(null),i=n.useRef(new Map).current;return(0,o.jsx)(s,{scope:t,itemMap:i,collectionRef:a,children:r})};d.displayName=t;let u=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,a=l(t,i(u,r).collectionRef);return(0,o.jsx)(c,{ref:a,children:n})});p.displayName=u;let m=e+"CollectionItemSlot",f="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:a,...s}=e,d=n.useRef(null),u=l(t,d),p=i(m,r);return n.useEffect(()=>(p.itemMap.set(d,{ref:d,...s}),()=>void p.itemMap.delete(d))),(0,o.jsx)(c,{[f]:"",ref:u,children:a})});return h.displayName=m,[{Provider:d,Slot:p,ItemSlot:h},function(t){let r=i(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(eR),[e_,eF]=ey(eR,[eT]),[eO,eq]=e_(eR),eL=n.forwardRef((e,t)=>(0,o.jsx)(eD.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(eD.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(eG,{...e,ref:t})})}));eL.displayName=eR;var eG=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:i,currentTabStopId:c,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:u,onEntryFocus:p,preventScrollOnEntryFocus:m=!1,...f}=e,h=n.useRef(null),b=l(t,h),g=eS(i),[x=null,v]=eP({prop:c,defaultProp:d,onChange:u}),[y,w]=n.useState(!1),k=eM(p),j=eI(r),N=n.useRef(!1),[C,M]=n.useState(0);return n.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(eA,k),()=>e.removeEventListener(eA,k)},[k]),(0,o.jsx)(eO,{scope:r,orientation:a,dir:g,loop:s,currentTabStopId:x,onItemFocus:n.useCallback(e=>v(e),[v]),onItemShiftTab:n.useCallback(()=>w(!0),[]),onFocusableItemAdd:n.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>M(e=>e-1),[]),children:(0,o.jsx)(eC.div,{tabIndex:y||0===C?-1:0,"data-orientation":a,...f,ref:b,style:{outline:"none",...e.style},onMouseDown:ev(e.onMouseDown,()=>{N.current=!0}),onFocus:ev(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!y){let t=new CustomEvent(eA,eE);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);eU([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),m)}}N.current=!1}),onBlur:ev(e.onBlur,()=>w(!1))})})}),e$="RovingFocusGroupItem",eV=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:s=!1,tabStopId:i,...l}=e,c=eN(),d=i||c,u=eq(e$,r),p=u.currentTabStopId===d,m=eI(r),{onFocusableItemAdd:f,onFocusableItemRemove:h}=u;return n.useEffect(()=>{if(a)return f(),()=>h()},[a,f,h]),(0,o.jsx)(eD.ItemSlot,{scope:r,id:d,focusable:a,active:s,children:(0,o.jsx)(eC.span,{tabIndex:p?0:-1,"data-orientation":u.orientation,...l,ref:t,onMouseDown:ev(e.onMouseDown,e=>{a?u.onItemFocus(d):e.preventDefault()}),onFocus:ev(e.onFocus,()=>u.onItemFocus(d)),onKeyDown:ev(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){u.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var o;let n=(o=e.key,"rtl"!==r?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return eW[n]}(e,u.orientation,u.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let o=r.indexOf(e.currentTarget);r=u.loop?function(e,t){return e.map((r,o)=>e[(t+o)%e.length])}(r,o+1):r.slice(o+1)}setTimeout(()=>eU(r))}})})})});eV.displayName=e$;var eW={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function eU(e,t=!1){let r=document.activeElement;for(let o of e)if(o===r||(o.focus({preventScroll:t}),document.activeElement!==r))return}var eB=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,a]=n.useState(),s=n.useRef({}),i=n.useRef(e),l=n.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=eK(s.current);l.current="mounted"===c?e:"none"},[c]),ew(()=>{let t=s.current,r=i.current;if(r!==e){let o=l.current,n=eK(t);e?d("MOUNT"):"none"===n||t?.display==="none"?d("UNMOUNT"):r&&o!==n?d("ANIMATION_OUT"):d("UNMOUNT"),i.current=e}},[e,d]),ew(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=eK(s.current).includes(r.animationName);if(r.target===o&&n&&(d("ANIMATION_END"),!i.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(l.current=eK(s.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{e&&(s.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),s=l(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||o.isPresent?n.cloneElement(a,{ref:s}):null};function eK(e){return e?.animationName||"none"}eB.displayName="Presence";var eH="Tabs",[eQ,eZ]=ey(eH,[eF]),eX=eF(),[eJ,eY]=eQ(eH),e0=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:s,orientation:i="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=eS(l),[p,m]=eP({prop:n,onChange:a,defaultProp:s});return(0,o.jsx)(eJ,{scope:r,baseId:eN(),value:p,onValueChange:m,orientation:i,dir:u,activationMode:c,children:(0,o.jsx)(eC.div,{dir:u,"data-orientation":i,...d,ref:t})})});e0.displayName=eH;var e1="TabsList",e2=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,s=eY(e1,r),i=eX(r);return(0,o.jsx)(eL,{asChild:!0,...i,orientation:s.orientation,dir:s.dir,loop:n,children:(0,o.jsx)(eC.div,{role:"tablist","aria-orientation":s.orientation,...a,ref:t})})});e2.displayName=e1;var e4="TabsTrigger",e3=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...s}=e,i=eY(e4,r),l=eX(r),c=e9(i.baseId,n),d=e7(i.baseId,n),u=n===i.value;return(0,o.jsx)(eV,{asChild:!0,...l,focusable:!a,active:u,children:(0,o.jsx)(eC.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":d,"data-state":u?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...s,ref:t,onMouseDown:ev(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(n)}),onKeyDown:ev(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(n)}),onFocus:ev(e.onFocus,()=>{let e="manual"!==i.activationMode;u||a||!e||i.onValueChange(n)})})})});e3.displayName=e4;var e5="TabsContent",e6=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:s,children:i,...l}=e,c=eY(e5,r),d=e9(c.baseId,a),u=e7(c.baseId,a),p=a===c.value,m=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,o.jsx)(eB,{present:s||p,children:({present:r})=>(0,o.jsx)(eC.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})})});function e9(e,t){return`${e}-trigger-${t}`}function e7(e,t){return`${e}-content-${t}`}function e8({className:e,...t}){return(0,o.jsx)(e0,{"data-slot":"tabs",className:ec("flex flex-col gap-2",e),...t})}function te({className:e,...t}){return(0,o.jsx)(e2,{"data-slot":"tabs-list",className:ec("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function tt({className:e,...t}){return(0,o.jsx)(e3,{"data-slot":"tabs-trigger",className:ec("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function tr({className:e,...t}){return(0,o.jsx)(e6,{"data-slot":"tabs-content",className:ec("flex-1 outline-none",e),...t})}e6.displayName=e5;let to=h("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function tn({className:e,variant:t,...r}){return(0,o.jsx)("div",{"data-slot":"alert",role:"alert",className:ec(to({variant:t}),e),...r})}function ta({className:e,...t}){return(0,o.jsx)("div",{"data-slot":"alert-description",className:ec("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}let ts=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ti=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var tl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let tc=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:s,iconNode:i,...l},c)=>(0,n.createElement)("svg",{ref:c,...tl,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:ti("lucide",a),...l},[...i.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(s)?s:[s]])),td=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...o},a)=>(0,n.createElement)(tc,{ref:a,iconNode:t,className:ti(`lucide-${ts(e)}`,r),...o}));return r.displayName=`${e}`,r},tu=td("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),tp=td("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),tm=td("Wallpaper",[["circle",{cx:"8",cy:"9",r:"2",key:"gjzl9d"}],["path",{d:"m9 17 6.1-6.1a2 2 0 0 1 2.81.01L22 15V5a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2",key:"69xh40"}],["path",{d:"M8 21h8",key:"1ev6f3"}],["path",{d:"M12 17v4",key:"1riwvh"}]]),tf=td("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),th=td("LockOpen",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]),tb=td("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),tg=td("DoorOpen",[["path",{d:"M13 4h3a2 2 0 0 1 2 2v14",key:"hrm0s9"}],["path",{d:"M2 20h3",key:"1gaodv"}],["path",{d:"M13 20h9",key:"s90cdi"}],["path",{d:"M10 12v.01",key:"vx6srw"}],["path",{d:"M13 4.562v16.157a1 1 0 0 1-1.242.97L5 20V5.562a2 2 0 0 1 1.515-1.94l4-1A2 2 0 0 1 13 4.561Z",key:"199qr4"}]]),tx=td("CableCar",[["path",{d:"M10 3h.01",key:"lbucoy"}],["path",{d:"M14 2h.01",key:"1k8aa1"}],["path",{d:"m2 9 20-5",key:"1kz0j5"}],["path",{d:"M12 12V6.5",key:"1vbrij"}],["rect",{width:"16",height:"10",x:"4",y:"12",rx:"3",key:"if91er"}],["path",{d:"M9 12v5",key:"3anwtq"}],["path",{d:"M15 12v5",key:"5xh3zn"}],["path",{d:"M4 17h16",key:"g4d7ey"}]]),tv=td("Radio",[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]]),ty=td("Footprints",[["path",{d:"M4 16v-2.38C4 11.5 2.97 10.5 3 8c.03-2.72 1.49-6 4.5-6C9.37 2 10 3.8 10 5.5c0 3.11-2 5.66-2 8.68V16a2 2 0 1 1-4 0Z",key:"1dudjm"}],["path",{d:"M20 20v-2.38c0-2.12 1.03-3.12 1-5.62-.03-2.72-1.49-6-4.5-6C14.63 6 14 7.8 14 9.5c0 3.11 2 5.66 2 8.68V20a2 2 0 1 0 4 0Z",key:"l2t8xc"}],["path",{d:"M16 17h4",key:"1dejxt"}],["path",{d:"M4 13h4",key:"1bwh8b"}]]),tw=td("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),tk=td("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),tj=td("MessageSquareX",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"m14.5 7.5-5 5",key:"3lb6iw"}],["path",{d:"m9.5 7.5 5 5",key:"ko136h"}]]),tN=td("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),tC=td("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),tM=td("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),tP=td("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),tz=td("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),tS=td("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);function tA(){let[e,t]=(0,n.useState)("zh"),r={zh:{title:"SimpleAntiCheat (SAC)",subtitle:"SCP: Secret Laboratory 服务端反作弊插件",developer:"开发者",features:"反作弊功能",community:"社区支持",appeals:"误封申诉",description:"高效反作弊系统，为您的 SCP:SL 服务器提供全面保护。",performanceNote:"性能影响几乎为零，保持服务器流畅运行",warningTitle:"重要提醒",warningText:"误判无法完全避免，我们正在持续改进算法。如遇误封请及时联系申诉。",contactInfo:"联系方式",appealContact:"申诉联系",qqGroup:"QQ群",discord:"Discord",githubProfile:"GitHub 主页",featuresData:[{icon:tu,name:"静默自瞄",desc:"检测并阻止自动瞄准作弊"},{icon:tp,name:"无后坐力",desc:"防止武器后坐力修改"},{icon:tm,name:"穿墙",desc:"检测非法穿墙行为"},{icon:tf,name:"SCP-939 漏洞",desc:"修复 SCP-939 相关漏洞利用"},{icon:tf,name:"SCP-106 漏洞",desc:"防止 SCP-106 漏洞滥用"},{icon:tf,name:"SCP-096 漏洞",desc:"阻止 SCP-096 异常行为"},{icon:th,name:"移动解锁",desc:"检测非法移动速度修改"},{icon:tb,name:"电网无伤",desc:"防止电网伤害绕过"},{icon:tg,name:"远程交互门",desc:"阻止远距离门操作"},{icon:tx,name:"远程交互电梯",desc:"防止远程电梯控制"},{icon:tv,name:"远程交互914",desc:"限制 SCP-914 远程操作"},{icon:ty,name:"Bigstep (大跨步)",desc:"检测异常步长移动"},{icon:tw,name:"BHop (连跳)",desc:"防止连续跳跃作弊"},{icon:tk,name:"假物品",desc:"反物品透视保护"},{icon:tj,name:"反敏感词",desc:"过滤不当聊天内容"}]},en:{title:"SimpleAntiCheat (SAC)",subtitle:"Anti-Cheat Plugin for SCP: Secret Laboratory Servers",developer:"Developer",features:"Anti-Cheat Features",community:"Community Support",appeals:"False Positive Appeals",description:"An efficient anti-cheat system providing comprehensive protection for your SCP:SL servers.",performanceNote:"Near-zero performance impact, keeping your server running smoothly",warningTitle:"Important Notice",warningText:"False positives cannot be completely avoided. We are continuously improving our algorithms. Please contact us promptly if you encounter a false ban.",contactInfo:"Contact Information",appealContact:"Appeal Contact",qqGroup:"QQ Group",discord:"Discord",githubProfile:"GitHub Profile",featuresData:[{icon:tu,name:"Silent Aimbot",desc:"Detect and prevent auto-aim cheats"},{icon:tp,name:"No Recoil",desc:"Prevent weapon recoil modifications"},{icon:tm,name:"Wallhack",desc:"Detect illegal wall penetration"},{icon:tf,name:"SCP-939 Exploit",desc:"Fix SCP-939 related exploits"},{icon:tf,name:"SCP-106 Exploit",desc:"Prevent SCP-106 exploit abuse"},{icon:tf,name:"SCP-096 Exploit",desc:"Block SCP-096 abnormal behaviors"},{icon:th,name:"Movement Unlock",desc:"Detect illegal movement speed modifications"},{icon:tb,name:"Tesla Gate Bypass",desc:"Prevent tesla gate damage bypass"},{icon:tg,name:"Remote Door Interaction",desc:"Block long-distance door operations"},{icon:tx,name:"Remote Elevator Interaction",desc:"Prevent remote elevator control"},{icon:tv,name:"Remote 914 Interaction",desc:"Limit SCP-914 remote operations"},{icon:ty,name:"Bigstep",desc:"Detect abnormal step length movement"},{icon:tw,name:"BHop (Bunny Hop)",desc:"Prevent continuous jump cheats"},{icon:tk,name:"Fake Items",desc:"Anti-item ESP protection"},{icon:tj,name:"Anti-Profanity",desc:"Filter inappropriate chat content"}]}}[e];return(0,o.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",children:[(0,o.jsx)("div",{className:"fixed inset-0 bg-[url('/abstract-digital-grid.png')] opacity-10"}),(0,o.jsx)("div",{className:"fixed inset-0 bg-gradient-to-t from-black/50 to-transparent"}),(0,o.jsxs)("div",{className:"relative z-10",children:[(0,o.jsx)("header",{className:"border-b border-blue-500/20 bg-black/20 backdrop-blur-sm",children:(0,o.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("img",{src:"/logo.png",alt:"SAC Logo",className:"w-12 h-12 rounded-lg object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,o.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden",children:(0,o.jsx)(tN,{className:"w-8 h-8 text-white"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-white",children:r.title}),(0,o.jsx)("p",{className:"text-blue-300 text-sm",children:r.subtitle})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eu,{variant:"zh"===e?"default":"outline",size:"sm",onClick:()=>t("zh"),className:"text-xs",children:"中文"}),(0,o.jsx)(eu,{variant:"en"===e?"default":"outline",size:"sm",onClick:()=>t("en"),className:"text-xs",children:"English"})]})]})})}),(0,o.jsx)("section",{className:"py-20",children:(0,o.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,o.jsx)("h2",{className:"text-5xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:r.title}),(0,o.jsx)("p",{className:"text-xl text-gray-300 mb-8 leading-relaxed",children:r.description}),(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-400 mb-8",children:[(0,o.jsx)(tp,{className:"w-5 h-5"}),(0,o.jsx)("span",{className:"text-sm font-medium",children:r.performanceNote})]}),(0,o.jsxs)(tn,{className:"max-w-2xl mx-auto bg-yellow-500/10 border-yellow-500/20",children:[(0,o.jsx)(tC,{className:"h-4 w-4 text-yellow-500"}),(0,o.jsxs)(ta,{className:"text-yellow-200",children:[(0,o.jsxs)("strong",{children:[r.warningTitle,":"]})," ",r.warningText]})]})]})})}),(0,o.jsx)("section",{className:"py-16",children:(0,o.jsx)("div",{className:"container mx-auto px-4",children:(0,o.jsxs)(e8,{defaultValue:"features",className:"max-w-6xl mx-auto",children:[(0,o.jsxs)(te,{className:"grid w-full grid-cols-3 bg-black/20 border border-blue-500/20",children:[(0,o.jsxs)(tt,{value:"features",className:"data-[state=active]:bg-blue-600",children:[(0,o.jsx)(tM,{className:"w-4 h-4 mr-2"}),r.features]}),(0,o.jsxs)(tt,{value:"community",className:"data-[state=active]:bg-blue-600",children:[(0,o.jsx)(tP,{className:"w-4 h-4 mr-2"}),r.community]}),(0,o.jsxs)(tt,{value:"appeals",className:"data-[state=active]:bg-blue-600",children:[(0,o.jsx)(tC,{className:"w-4 h-4 mr-2"}),r.appeals]})]}),(0,o.jsx)(tr,{value:"features",className:"mt-8",children:(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.featuresData.map((e,t)=>(0,o.jsxs)(ep,{className:"bg-black/20 border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10",children:[(0,o.jsx)(em,{className:"pb-3",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,o.jsx)(e.icon,{className:"w-5 h-5 text-white"})}),(0,o.jsx)(ef,{className:"text-white text-lg",children:e.name})]})}),(0,o.jsx)(eb,{children:(0,o.jsx)(eh,{className:"text-gray-300",children:e.desc})})]},t))})}),(0,o.jsx)(tr,{value:"community",className:"mt-8",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto",children:[(0,o.jsxs)(ep,{className:"bg-black/20 border-blue-500/20",children:[(0,o.jsx)(em,{children:(0,o.jsxs)(ef,{className:"text-white flex items-center",children:[(0,o.jsx)(tz,{className:"w-5 h-5 mr-2 text-green-500"}),r.qqGroup]})}),(0,o.jsxs)(eb,{children:[(0,o.jsx)("p",{className:"text-gray-300 mb-4",children:"zh"===e?"加入我们的QQ群获取支持和交流":"Join our QQ group for support and discussion"}),(0,o.jsx)(ex,{variant:"secondary",className:"text-lg px-4 py-2 bg-green-500/20 text-green-300 border-green-500/20",children:"1007642619"})]})]}),(0,o.jsxs)(ep,{className:"bg-black/20 border-blue-500/20",children:[(0,o.jsx)(em,{children:(0,o.jsxs)(ef,{className:"text-white flex items-center",children:[(0,o.jsx)(tz,{className:"w-5 h-5 mr-2 text-purple-500"}),"Discord"]})}),(0,o.jsxs)(eb,{children:[(0,o.jsx)("p",{className:"text-gray-300 mb-4",children:"zh"===e?"加入Discord服务器进行国际交流":"Join our Discord server for international communication"}),(0,o.jsx)(eu,{asChild:!0,variant:"outline",className:"border-purple-500/20 text-purple-300 hover:bg-purple-500/10",children:(0,o.jsxs)("a",{href:"https://discord.gg/PWRXKE6aNM",target:"_blank",rel:"noopener noreferrer",children:["zh"===e?"加入Discord":"Join Discord",(0,o.jsx)(tS,{className:"w-4 h-4 ml-2"})]})})]})]})]})}),(0,o.jsx)(tr,{value:"appeals",className:"mt-8",children:(0,o.jsxs)(ep,{className:"bg-red-500/10 border-red-500/20 max-w-2xl mx-auto",children:[(0,o.jsxs)(em,{children:[(0,o.jsxs)(ef,{className:"text-white flex items-center",children:[(0,o.jsx)(tC,{className:"w-5 h-5 mr-2 text-red-400"}),r.appeals]}),(0,o.jsx)(eh,{className:"text-red-200",children:"zh"===e?"如果您认为被误封，请通过以下方式联系我们":"If you believe you were falsely banned, please contact us through the following methods"})]}),(0,o.jsxs)(eb,{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"bg-black/20 p-4 rounded-lg border border-red-500/20",children:[(0,o.jsx)("h4",{className:"text-white font-semibold mb-2",children:r.appealContact}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(tz,{className:"w-4 h-4 text-blue-400"}),(0,o.jsx)("span",{className:"text-gray-300",children:"QQ: "}),(0,o.jsx)(ex,{variant:"secondary",className:"bg-blue-500/20 text-blue-300 border-blue-500/20",children:"3298837494"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(tz,{className:"w-4 h-4 text-purple-400"}),(0,o.jsx)("span",{className:"text-gray-300",children:"Discord: "}),(0,o.jsx)(eu,{asChild:!0,variant:"link",className:"p-0 h-auto text-purple-300 hover:text-purple-200",children:(0,o.jsx)("a",{href:"https://discord.gg/PWRXKE6aNM",target:"_blank",rel:"noopener noreferrer",children:"PWRXKE6aNM"})})]})]})]}),(0,o.jsxs)(tn,{className:"bg-yellow-500/10 border-yellow-500/20",children:[(0,o.jsx)(tC,{className:"h-4 w-4 text-yellow-500"}),(0,o.jsx)(ta,{className:"text-yellow-200",children:"zh"===e?"请通过以上任意一种方式进行申诉。":"Please appeal through any of the above methods."})]})]})]})})]})})}),(0,o.jsx)("footer",{className:"border-t border-blue-500/20 bg-black/20 backdrop-blur-sm py-8",children:(0,o.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,o.jsx)("img",{src:"/logo.png",alt:"SAC Logo",className:"w-6 h-6 rounded-lg object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,o.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden",children:(0,o.jsx)(tN,{className:"w-6 h-6 text-blue-400"})}),(0,o.jsx)("span",{className:"text-white font-semibold",children:"SimpleAntiCheat (SAC)"})]}),(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:"zh"===e?"\xa9 2025 Web Developed by kldhsh123。":"\xa9 2025 Web Developed by kldhsh123."})]})})]})]})}},6391:()=>{},6757:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var o=r(5466),n=r(6525),a=r(1861),s=r.n(a),i=r(1546),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7941)),"F:\\Desktop\\本地项目\\sac-anticheat\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,7006)),"F:\\Desktop\\本地项目\\sac-anticheat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,907,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,5958,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,6463,23)),"next/dist/client/components/unauthorized-error"]}],d=["F:\\Desktop\\本地项目\\sac-anticheat\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7006:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var o=r(5322),n=r(9227),a=r.n(n),s=r(3603),i=r.n(s);r(6391);let l={title:"SimpleAntiCheat (SAC)",description:"SimpleAntiCheat (SAC)",generator:"kldhsh.top",icons:{icon:"/ico.ico"}};function c({children:e}){return(0,o.jsxs)("html",{lang:"en",children:[(0,o.jsx)("head",{children:(0,o.jsx)("style",{children:`
html {
  font-family: ${a().style.fontFamily};
  --font-sans: ${a().variable};
  --font-mono: ${i().variable};
}
        `})}),(0,o.jsx)("body",{children:e})]})}},7303:()=>{},7597:(e,t,r)=>{Promise.resolve().then(r.bind(r,7941))},7941:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(4810).registerClientReference)(function(){throw Error("Attempted to call the default export of \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"F:\\Desktop\\本地项目\\sac-anticheat\\app\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[337],()=>r(6757));module.exports=o})();