"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Zap, Eye, Target, WallpaperIcon as Wall, Bug, Unlock, Wifi, DoorOpenIcon as Door, CableCarIcon as Elevator, Radio, Footprints, ArrowUp, Package, MessageSquareX, Github, MessageCircle, Users, AlertTriangle, Globe, ExternalLink } from 'lucide-react'

export default function SimpleAntiCheatPage() {
  const [language, setLanguage] = useState<'zh' | 'en'>('zh')

  const content = {
    zh: {
      title: "SimpleAntiCheat (SAC)",
      subtitle: "SCP: Secret Laboratory 服务端反作弊插件",
      developer: "开发者",
      features: "反作弊功能",
      community: "社区支持",
      appeals: "误封申诉",
      description: "高效反作弊系统，为您的 SCP:SL 服务器提供全面保护。",
      performanceNote: "性能影响几乎为零，保持服务器流畅运行",
      warningTitle: "重要提醒",
      warningText: "误判无法完全避免，我们正在持续改进算法。如遇误封请及时联系申诉。",
      contactInfo: "联系方式",
      appealContact: "申诉联系",
      qqGroup: "QQ群",
      discord: "Discord",
      githubProfile: "GitHub 主页",
      featuresData: [
        { icon: Target, name: "静默自瞄", desc: "检测并阻止自动瞄准作弊" },
        { icon: Zap, name: "无后坐力", desc: "防止武器后坐力修改" },
        { icon: Wall, name: "穿墙", desc: "检测非法穿墙行为" },
        { icon: Bug, name: "SCP-939 漏洞", desc: "修复 SCP-939 相关漏洞利用" },
        { icon: Bug, name: "SCP-106 漏洞", desc: "防止 SCP-106 漏洞滥用" },
        { icon: Bug, name: "SCP-096 漏洞", desc: "阻止 SCP-096 异常行为" },
        { icon: Unlock, name: "移动解锁", desc: "检测非法移动速度修改" },
        { icon: Wifi, name: "电网无伤", desc: "防止电网伤害绕过" },
        { icon: Door, name: "远程交互门", desc: "阻止远距离门操作" },
        { icon: Elevator, name: "远程交互电梯", desc: "防止远程电梯控制" },
        { icon: Radio, name: "远程交互914", desc: "限制 SCP-914 远程操作" },
        { icon: Footprints, name: "Bigstep (大跨步)", desc: "检测异常步长移动" },
        { icon: ArrowUp, name: "BHop (连跳)", desc: "防止连续跳跃作弊" },
        { icon: Package, name: "假物品", desc: "反物品透视保护" },
        { icon: MessageSquareX, name: "反敏感词", desc: "过滤不当聊天内容" }
      ]
    },
    en: {
      title: "SimpleAntiCheat (SAC)",
      subtitle: "Anti-Cheat Plugin for SCP: Secret Laboratory Servers",
      developer: "Developer",
      features: "Anti-Cheat Features",
      community: "Community Support",
      appeals: "False Positive Appeals",
      description: "An efficient anti-cheat system providing comprehensive protection for your SCP:SL servers.",
      performanceNote: "Near-zero performance impact, keeping your server running smoothly",
      warningTitle: "Important Notice",
      warningText: "False positives cannot be completely avoided. We are continuously improving our algorithms. Please contact us promptly if you encounter a false ban.",
      contactInfo: "Contact Information",
      appealContact: "Appeal Contact",
      qqGroup: "QQ Group",
      discord: "Discord",
      githubProfile: "GitHub Profile",
      featuresData: [
        { icon: Target, name: "Silent Aimbot", desc: "Detect and prevent auto-aim cheats" },
        { icon: Zap, name: "No Recoil", desc: "Prevent weapon recoil modifications" },
        { icon: Wall, name: "Wallhack", desc: "Detect illegal wall penetration" },
        { icon: Bug, name: "SCP-939 Exploit", desc: "Fix SCP-939 related exploits" },
        { icon: Bug, name: "SCP-106 Exploit", desc: "Prevent SCP-106 exploit abuse" },
        { icon: Bug, name: "SCP-096 Exploit", desc: "Block SCP-096 abnormal behaviors" },
        { icon: Unlock, name: "Movement Unlock", desc: "Detect illegal movement speed modifications" },
        { icon: Wifi, name: "Tesla Gate Bypass", desc: "Prevent tesla gate damage bypass" },
        { icon: Door, name: "Remote Door Interaction", desc: "Block long-distance door operations" },
        { icon: Elevator, name: "Remote Elevator Interaction", desc: "Prevent remote elevator control" },
        { icon: Radio, name: "Remote 914 Interaction", desc: "Limit SCP-914 remote operations" },
        { icon: Footprints, name: "Bigstep", desc: "Detect abnormal step length movement" },
        { icon: ArrowUp, name: "BHop (Bunny Hop)", desc: "Prevent continuous jump cheats" },
        { icon: Package, name: "Fake Items", desc: "Anti-item ESP protection" },
        { icon: MessageSquareX, name: "Anti-Profanity", desc: "Filter inappropriate chat content" }
      ]
    }
  }

  const t = content[language]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[url('/abstract-digital-grid.png')] opacity-10"></div>
      <div className="fixed inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
      
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b border-blue-500/20 bg-black/20 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src="/logo.png"
                  alt="SAC Logo"
                  className="w-12 h-12 rounded-lg object-cover"
                  onError={(e) => {
                    // 如果图片加载失败，显示Shield图标作为后备
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling.style.display = 'flex';
                  }}
                />
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">{t.title}</h1>
                  <p className="text-blue-300 text-sm">{t.subtitle}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant={language === 'zh' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setLanguage('zh')}
                  className="text-xs"
                >
                  中文
                </Button>
                <Button
                  variant={language === 'en' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setLanguage('en')}
                  className="text-xs"
                >
                  English
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-5xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                {t.title}
              </h2>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                {t.description}
              </p>
              <div className="flex items-center justify-center space-x-2 text-green-400 mb-8">
                <Zap className="w-5 h-5" />
                <span className="text-sm font-medium">{t.performanceNote}</span>
              </div>
              
              <Alert className="max-w-2xl mx-auto bg-yellow-500/10 border-yellow-500/20">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <AlertDescription className="text-yellow-200">
                  <strong>{t.warningTitle}:</strong> {t.warningText}
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <Tabs defaultValue="features" className="max-w-6xl mx-auto">
              <TabsList className="grid w-full grid-cols-3 bg-black/20 border border-blue-500/20">
                <TabsTrigger value="features" className="data-[state=active]:bg-blue-600">
                  <Eye className="w-4 h-4 mr-2" />
                  {t.features}
                </TabsTrigger>
                <TabsTrigger value="community" className="data-[state=active]:bg-blue-600">
                  <Users className="w-4 h-4 mr-2" />
                  {t.community}
                </TabsTrigger>
                <TabsTrigger value="appeals" className="data-[state=active]:bg-blue-600">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  {t.appeals}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="features" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {t.featuresData.map((feature, index) => (
                    <Card key={index} className="bg-black/20 border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
                      <CardHeader className="pb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <feature.icon className="w-5 h-5 text-white" />
                          </div>
                          <CardTitle className="text-white text-lg">{feature.name}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-gray-300">
                          {feature.desc}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="community" className="mt-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                  <Card className="bg-black/20 border-blue-500/20">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center">
                        <MessageCircle className="w-5 h-5 mr-2 text-green-500" />
                        {t.qqGroup}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-300 mb-4">
                        {language === 'zh' ? '加入我们的QQ群获取支持和交流' : 'Join our QQ group for support and discussion'}
                      </p>
                      <Badge variant="secondary" className="text-lg px-4 py-2 bg-green-500/20 text-green-300 border-green-500/20">
                        1007642619
                      </Badge>
                    </CardContent>
                  </Card>

                  <Card className="bg-black/20 border-blue-500/20">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center">
                        <MessageCircle className="w-5 h-5 mr-2 text-purple-500" />
                        Discord
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-300 mb-4">
                        {language === 'zh' ? '加入Discord服务器进行国际交流' : 'Join our Discord server for international communication'}
                      </p>
                      <Button asChild variant="outline" className="border-purple-500/20 text-purple-300 hover:bg-purple-500/10">
                        <a href="https://discord.gg/PWRXKE6aNM" target="_blank" rel="noopener noreferrer">
                          {language === 'zh' ? '加入Discord' : 'Join Discord'}
                          <ExternalLink className="w-4 h-4 ml-2" />
                        </a>
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="appeals" className="mt-8">
                <Card className="bg-red-500/10 border-red-500/20 max-w-2xl mx-auto">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center">
                      <AlertTriangle className="w-5 h-5 mr-2 text-red-400" />
                      {t.appeals}
                    </CardTitle>
                    <CardDescription className="text-red-200">
                      {language === 'zh' 
                        ? '如果您认为被误封，请通过以下方式联系我们'
                        : 'If you believe you were falsely banned, please contact us through the following methods'
                      }
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="bg-black/20 p-4 rounded-lg border border-red-500/20">
                      <h4 className="text-white font-semibold mb-2">{t.appealContact}</h4>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <MessageCircle className="w-4 h-4 text-blue-400" />
                          <span className="text-gray-300">QQ: </span>
                          <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/20">
                            3298837494
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Users className="w-4 h-4 text-green-400" />
                          <span className="text-gray-300">{t.qqGroup}: </span>
                          <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/20">
                            1007642619
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MessageCircle className="w-4 h-4 text-purple-400" />
                          <span className="text-gray-300">Discord: </span>
                          <Button asChild variant="link" className="p-0 h-auto text-purple-300 hover:text-purple-200">
                            <a href="https://discord.gg/PWRXKE6aNM" target="_blank" rel="noopener noreferrer">
                              PWRXKE6aNM
                            </a>
                          </Button>
                        </div>
                      </div>
                    </div>
                    <Alert className="bg-yellow-500/10 border-yellow-500/20">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <AlertDescription className="text-yellow-200">
                        {language === 'zh' 
                          ? '请提供详细的游戏信息和时间，以便我们快速处理您的申诉。'
                          : 'Please provide detailed game information and timestamps for us to process your appeal quickly.'
                        }
                      </AlertDescription>
                    </Alert>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* Footer */}
        <footer className="border-t border-blue-500/20 bg-black/20 backdrop-blur-sm py-8">
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <img
                src="/logo.png"
                alt="SAC Logo"
                className="w-6 h-6 rounded-lg object-cover"
                onError={(e) => {
                  // 如果图片加载失败，显示Shield图标作为后备
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden">
                <Shield className="w-6 h-6 text-blue-400" />
              </div>
              <span className="text-white font-semibold">SimpleAntiCheat (SAC)</span>
            </div>
            <p className="text-gray-400 text-sm">
              {language === 'zh' 
                ? '© 2025 Web Developed by kldhsh123。'
                : '© 2025 Web Developed by kldhsh123.'
              }
            </p>
          </div>
        </footer>
      </div>
    </div>
  )
}
