"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleAntiCheatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wallpaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/door-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/cable-car.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/footprints.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/message-square-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowUp,Bug,CableCarIcon,DoorOpenIcon,ExternalLink,Eye,Footprints,MessageCircle,MessageSquareX,Package,Radio,Shield,Target,Unlock,Users,WallpaperIcon,Wifi,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SimpleAntiCheatPage() {\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('zh');\n    const content = {\n        zh: {\n            title: \"SimpleAntiCheat (SAC)\",\n            subtitle: \"SCP: Secret Laboratory 服务端反作弊插件\",\n            developer: \"开发者\",\n            features: \"反作弊功能\",\n            community: \"社区支持\",\n            appeals: \"误封申诉\",\n            description: \"高效反作弊系统，为您的 SCP:SL 服务器提供全面保护。\",\n            performanceNote: \"性能影响几乎为零，保持服务器流畅运行\",\n            warningTitle: \"重要提醒\",\n            warningText: \"误判无法完全避免，我们正在持续改进算法。如遇误封请及时联系申诉。\",\n            contactInfo: \"联系方式\",\n            appealContact: \"申诉联系\",\n            qqGroup: \"QQ群\",\n            discord: \"Discord\",\n            githubProfile: \"GitHub 主页\",\n            featuresData: [\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    name: \"静默自瞄\",\n                    desc: \"检测并阻止自动瞄准作弊\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    name: \"无后坐力\",\n                    desc: \"防止武器后坐力修改\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    name: \"穿墙\",\n                    desc: \"检测非法穿墙行为\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-939 漏洞\",\n                    desc: \"修复 SCP-939 相关漏洞利用\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-106 漏洞\",\n                    desc: \"防止 SCP-106 漏洞滥用\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-096 漏洞\",\n                    desc: \"阻止 SCP-096 异常行为\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    name: \"移动解锁\",\n                    desc: \"检测非法移动速度修改\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    name: \"电网无伤\",\n                    desc: \"防止电网伤害绕过\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    name: \"远程交互门\",\n                    desc: \"阻止远距离门操作\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    name: \"远程交互电梯\",\n                    desc: \"防止远程电梯控制\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"远程交互914\",\n                    desc: \"限制 SCP-914 远程操作\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Bigstep (大跨步)\",\n                    desc: \"检测异常步长移动\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"BHop (连跳)\",\n                    desc: \"防止连续跳跃作弊\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"假物品\",\n                    desc: \"反物品透视保护\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"反敏感词\",\n                    desc: \"过滤不当聊天内容\"\n                }\n            ]\n        },\n        en: {\n            title: \"SimpleAntiCheat (SAC)\",\n            subtitle: \"Anti-Cheat Plugin for SCP: Secret Laboratory Servers\",\n            developer: \"Developer\",\n            features: \"Anti-Cheat Features\",\n            community: \"Community Support\",\n            appeals: \"False Positive Appeals\",\n            description: \"An efficient anti-cheat system providing comprehensive protection for your SCP:SL servers.\",\n            performanceNote: \"Near-zero performance impact, keeping your server running smoothly\",\n            warningTitle: \"Important Notice\",\n            warningText: \"False positives cannot be completely avoided. We are continuously improving our algorithms. Please contact us promptly if you encounter a false ban.\",\n            contactInfo: \"Contact Information\",\n            appealContact: \"Appeal Contact\",\n            qqGroup: \"QQ Group\",\n            discord: \"Discord\",\n            githubProfile: \"GitHub Profile\",\n            featuresData: [\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    name: \"Silent Aimbot\",\n                    desc: \"Detect and prevent auto-aim cheats\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    name: \"No Recoil\",\n                    desc: \"Prevent weapon recoil modifications\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    name: \"Wallhack\",\n                    desc: \"Detect illegal wall penetration\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-939 Exploit\",\n                    desc: \"Fix SCP-939 related exploits\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-106 Exploit\",\n                    desc: \"Prevent SCP-106 exploit abuse\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    name: \"SCP-096 Exploit\",\n                    desc: \"Block SCP-096 abnormal behaviors\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    name: \"Movement Unlock\",\n                    desc: \"Detect illegal movement speed modifications\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    name: \"Tesla Gate Bypass\",\n                    desc: \"Prevent tesla gate damage bypass\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                    name: \"Remote Door Interaction\",\n                    desc: \"Block long-distance door operations\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                    name: \"Remote Elevator Interaction\",\n                    desc: \"Prevent remote elevator control\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                    name: \"Remote 914 Interaction\",\n                    desc: \"Limit SCP-914 remote operations\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                    name: \"Bigstep\",\n                    desc: \"Detect abnormal step length movement\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                    name: \"BHop (Bunny Hop)\",\n                    desc: \"Prevent continuous jump cheats\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                    name: \"Fake Items\",\n                    desc: \"Anti-item ESP protection\"\n                },\n                {\n                    icon: _barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                    name: \"Anti-Profanity\",\n                    desc: \"Filter inappropriate chat content\"\n                }\n            ]\n        }\n    };\n    const t = content[language];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-[url('/abstract-digital-grid.png')] opacity-10\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gradient-to-t from-black/50 to-transparent\"\n            }, void 0, false, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-blue-500/20 bg-black/20 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/logo.png\",\n                                                alt: \"SAC Logo\",\n                                                className: \"w-12 h-12 rounded-lg object-cover\",\n                                                onError: (e)=>{\n                                                    // 如果图片加载失败，显示Shield图标作为后备\n                                                    e.currentTarget.style.display = 'none';\n                                                    e.currentTarget.nextElementSibling.style.display = 'flex';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: t.title\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: t.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: language === 'zh' ? 'default' : 'outline',\n                                                size: \"sm\",\n                                                onClick: ()=>setLanguage('zh'),\n                                                className: \"text-xs\",\n                                                children: \"中文\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: language === 'en' ? 'default' : 'outline',\n                                                size: \"sm\",\n                                                onClick: ()=>setLanguage('en'),\n                                                className: \"text-xs\",\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                        children: t.title\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed\",\n                                        children: t.description\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2 text-green-400 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: t.performanceNote\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                        className: \"max-w-2xl mx-auto bg-yellow-500/10 border-yellow-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                className: \"text-yellow-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t.warningTitle,\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" \",\n                                                    t.warningText\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                                defaultValue: \"features\",\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 bg-black/20 border border-blue-500/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"features\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.features\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"community\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.community\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                                value: \"appeals\",\n                                                className: \"data-[state=active]:bg-blue-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t.appeals\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"features\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: t.featuresData.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            className: \"pb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                            className: \"w-5 h-5 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                        className: \"text-white text-lg\",\n                                                                        children: feature.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                                className: \"text-gray-300\",\n                                                                children: feature.desc\n                                                            }, void 0, false, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"community\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-white flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-5 h-5 mr-2 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t.qqGroup\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 mb-4\",\n                                                                    children: language === 'zh' ? '加入我们的QQ群获取支持和交流' : 'Join our QQ group for support and discussion'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-lg px-4 py-2 bg-green-500/20 text-green-300 border-green-500/20\",\n                                                                    children: \"1007642619\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"bg-black/20 border-blue-500/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-white flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-5 h-5 mr-2 text-purple-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Discord\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 mb-4\",\n                                                                    children: language === 'zh' ? '加入Discord服务器进行国际交流' : 'Join our Discord server for international communication'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    asChild: true,\n                                                                    variant: \"outline\",\n                                                                    className: \"border-purple-500/20 text-purple-300 hover:bg-purple-500/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"https://discord.gg/PWRXKE6aNM\",\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        children: [\n                                                                            language === 'zh' ? '加入Discord' : 'Join Discord',\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"w-4 h-4 ml-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 238,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                        value: \"appeals\",\n                                        className: \"mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-red-500/10 border-red-500/20 max-w-2xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-white flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5 mr-2 text-red-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                t.appeals\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                            className: \"text-red-200\",\n                                                            children: language === 'zh' ? '如果您认为被误封，请通过以下方式联系我们' : 'If you believe you were falsely banned, please contact us through the following methods'\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-black/20 p-4 rounded-lg border border-red-500/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-white font-semibold mb-2\",\n                                                                    children: t.appealContact\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-blue-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: \"QQ: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 266,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"bg-blue-500/20 text-blue-300 border-blue-500/20\",\n                                                                                    children: \"3298837494\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 267,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-green-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 272,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: [\n                                                                                        t.qqGroup,\n                                                                                        \": \"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"bg-green-500/20 text-green-300 border-green-500/20\",\n                                                                                    children: \"1007642619\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-purple-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 279,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-300\",\n                                                                                    children: \"Discord: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    asChild: true,\n                                                                                    variant: \"link\",\n                                                                                    className: \"p-0 h-auto text-purple-300 hover:text-purple-200\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                        href: \"https://discord.gg/PWRXKE6aNM\",\n                                                                                        target: \"_blank\",\n                                                                                        rel: \"noopener noreferrer\",\n                                                                                        children: \"PWRXKE6aNM\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                                            className: \"bg-yellow-500/10 border-yellow-500/20\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                                                    className: \"text-yellow-200\",\n                                                                    children: language === 'zh' ? '请提供详细的游戏信息和时间，以便我们快速处理您的申诉。' : 'Please provide detailed game information and timestamps for us to process your appeal quickly.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"border-t border-blue-500/20 bg-black/20 backdrop-blur-sm py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/logo.png\",\n                                            alt: \"SAC Logo\",\n                                            className: \"w-6 h-6 rounded-lg object-cover\",\n                                            onError: (e)=>{\n                                                // 如果图片加载失败，显示Shield图标作为后备\n                                                e.currentTarget.style.display = 'none';\n                                                e.currentTarget.nextElementSibling.style.display = 'flex';\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowUp_Bug_CableCarIcon_DoorOpenIcon_ExternalLink_Eye_Footprints_MessageCircle_MessageSquareX_Package_Radio_Shield_Target_Unlock_Users_WallpaperIcon_Wifi_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold\",\n                                            children: \"SimpleAntiCheat (SAC)\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-sm\",\n                                    children: language === 'zh' ? '© 2025 Web Developed by kldhsh123。' : '© 2025 Web Developed by kldhsh123.'\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Desktop\\\\本地项目\\\\sac-anticheat\\\\app\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleAntiCheatPage, \"2kR9RvDLUSiL698GW5sOHZriVSM=\");\n_c = SimpleAntiCheatPage;\nvar _c;\n$RefreshReg$(_c, \"SimpleAntiCheatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});