
const http = require('http');
const fs = require('fs');
const path = require('path');

// 简单的MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 解析URL获取文件路径
  let filePath = path.join(__dirname, 'out', req.url === '/' ? 'index.html' : req.url);
  
  // 如果路径没有扩展名，假设是目录，尝试查找index.html
  if (!path.extname(filePath)) {
    filePath = path.join(filePath, 'index.html');
  }

  // 获取文件扩展名
  const extname = String(path.extname(filePath)).toLowerCase();
  const contentType = mimeTypes[extname] || 'application/octet-stream';

  // 读取文件
  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在
        res.writeHead(404);
        res.end('404 Not Found: ' + req.url);
      } else {
        // 服务器错误
        res.writeHead(500);
        res.end('500 Server Error: ' + error.code);
      }
    } else {
      // 成功响应
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
});

// 启动服务器
server.listen(3000, () => {
  console.log('预览服务器已启动，请访问 http://localhost:3000');
  console.log('按Ctrl+C可以停止服务器');
});
