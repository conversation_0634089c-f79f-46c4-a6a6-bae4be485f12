(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5851:(e,t,s)=>{Promise.resolve().then(s.bind(s,6757))},6757:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var a=s(4),r=s(3176),n=s(9249),i=s(4552),l=s(5894),o=s(7399);function c(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,o.QP)((0,l.$)(t))}let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function m(e){let{className:t,variant:s,size:r,asChild:i=!1,...l}=e,o=i?n.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:c(d({variant:s,size:r,className:t})),...l})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:c("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function u(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:c("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function p(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:c("leading-none font-semibold",t),...s})}function g(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:c("text-muted-foreground text-sm",t),...s})}function h(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:c("px-6",t),...s})}let b=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function v(e){let{className:t,variant:s,asChild:r=!1,...i}=e,l=r?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:c(b({variant:s}),t),...i})}var f=s(2142);function j(e){let{className:t,...s}=e;return(0,a.jsx)(f.bL,{"data-slot":"tabs",className:c("flex flex-col gap-2",t),...s})}function N(e){let{className:t,...s}=e;return(0,a.jsx)(f.B8,{"data-slot":"tabs-list",className:c("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s})}function y(e){let{className:t,...s}=e;return(0,a.jsx)(f.l9,{"data-slot":"tabs-trigger",className:c("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function w(e){let{className:t,...s}=e;return(0,a.jsx)(f.UC,{"data-slot":"tabs-content",className:c("flex-1 outline-none",t),...s})}let A=(0,i.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function k(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:c(A({variant:s}),t),...r})}function C(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:c("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s})}var P=s(160),S=s(6588),z=s(8137),D=s(9565),_=s(845),E=s(8752),Q=s(4564),T=s(5445),B=s(9280),F=s(8577),I=s(9004),L=s(6488),G=s(3953),R=s(348),W=s(9306),q=s(9954),H=s(7603),X=s(1939),M=s(3713);function V(){let[e,t]=(0,r.useState)("zh"),s={zh:{title:"SimpleAntiCheat (SAC)",subtitle:"SCP: Secret Laboratory 服务端反作弊插件",developer:"开发者",features:"反作弊功能",community:"社区支持",appeals:"误封申诉",description:"高效反作弊系统，为您的 SCP:SL 服务器提供全面保护。",performanceNote:"性能影响几乎为零，保持服务器流畅运行",warningTitle:"重要提醒",warningText:"误判无法完全避免，我们正在持续改进算法。如遇误封请及时联系申诉。",contactInfo:"联系方式",appealContact:"申诉联系",qqGroup:"QQ群",discord:"Discord",githubProfile:"GitHub 主页",featuresData:[{icon:P.A,name:"静默自瞄",desc:"检测并阻止自动瞄准作弊"},{icon:S.A,name:"无后坐力",desc:"防止武器后坐力修改"},{icon:z.A,name:"穿墙",desc:"检测非法穿墙行为"},{icon:D.A,name:"SCP-939 漏洞",desc:"修复 SCP-939 相关漏洞利用"},{icon:D.A,name:"SCP-106 漏洞",desc:"防止 SCP-106 漏洞滥用"},{icon:D.A,name:"SCP-096 漏洞",desc:"阻止 SCP-096 异常行为"},{icon:_.A,name:"移动解锁",desc:"检测非法移动速度修改"},{icon:E.A,name:"电网无伤",desc:"防止电网伤害绕过"},{icon:Q.A,name:"远程交互门",desc:"阻止远距离门操作"},{icon:T.A,name:"远程交互电梯",desc:"防止远程电梯控制"},{icon:B.A,name:"远程交互914",desc:"限制 SCP-914 远程操作"},{icon:F.A,name:"Bigstep (大跨步)",desc:"检测异常步长移动"},{icon:I.A,name:"BHop (连跳)",desc:"防止连续跳跃作弊"},{icon:L.A,name:"假物品",desc:"反物品透视保护"},{icon:G.A,name:"反敏感词",desc:"过滤不当聊天内容"}]},en:{title:"SimpleAntiCheat (SAC)",subtitle:"Anti-Cheat Plugin for SCP: Secret Laboratory Servers",developer:"Developer",features:"Anti-Cheat Features",community:"Community Support",appeals:"False Positive Appeals",description:"An efficient anti-cheat system providing comprehensive protection for your SCP:SL servers.",performanceNote:"Near-zero performance impact, keeping your server running smoothly",warningTitle:"Important Notice",warningText:"False positives cannot be completely avoided. We are continuously improving our algorithms. Please contact us promptly if you encounter a false ban.",contactInfo:"Contact Information",appealContact:"Appeal Contact",qqGroup:"QQ Group",discord:"Discord",githubProfile:"GitHub Profile",featuresData:[{icon:P.A,name:"Silent Aimbot",desc:"Detect and prevent auto-aim cheats"},{icon:S.A,name:"No Recoil",desc:"Prevent weapon recoil modifications"},{icon:z.A,name:"Wallhack",desc:"Detect illegal wall penetration"},{icon:D.A,name:"SCP-939 Exploit",desc:"Fix SCP-939 related exploits"},{icon:D.A,name:"SCP-106 Exploit",desc:"Prevent SCP-106 exploit abuse"},{icon:D.A,name:"SCP-096 Exploit",desc:"Block SCP-096 abnormal behaviors"},{icon:_.A,name:"Movement Unlock",desc:"Detect illegal movement speed modifications"},{icon:E.A,name:"Tesla Gate Bypass",desc:"Prevent tesla gate damage bypass"},{icon:Q.A,name:"Remote Door Interaction",desc:"Block long-distance door operations"},{icon:T.A,name:"Remote Elevator Interaction",desc:"Prevent remote elevator control"},{icon:B.A,name:"Remote 914 Interaction",desc:"Limit SCP-914 remote operations"},{icon:F.A,name:"Bigstep",desc:"Detect abnormal step length movement"},{icon:I.A,name:"BHop (Bunny Hop)",desc:"Prevent continuous jump cheats"},{icon:L.A,name:"Fake Items",desc:"Anti-item ESP protection"},{icon:G.A,name:"Anti-Profanity",desc:"Filter inappropriate chat content"}]}}[e];return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-[url('/abstract-digital-grid.png')] opacity-10"}),(0,a.jsx)("div",{className:"fixed inset-0 bg-gradient-to-t from-black/50 to-transparent"}),(0,a.jsxs)("div",{className:"relative z-10",children:[(0,a.jsx)("header",{className:"border-b border-blue-500/20 bg-black/20 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("img",{src:"/logo.png",alt:"SAC Logo",className:"w-12 h-12 rounded-lg object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden",children:(0,a.jsx)(R.A,{className:"w-8 h-8 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:s.title}),(0,a.jsx)("p",{className:"text-blue-300 text-sm",children:s.subtitle})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m,{variant:"zh"===e?"default":"outline",size:"sm",onClick:()=>t("zh"),className:"text-xs",children:"中文"}),(0,a.jsx)(m,{variant:"en"===e?"default":"outline",size:"sm",onClick:()=>t("en"),className:"text-xs",children:"English"})]})]})})}),(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-5xl font-bold text-white mb-6 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:s.title}),(0,a.jsx)("p",{className:"text-xl text-gray-300 mb-8 leading-relaxed",children:s.description}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-400 mb-8",children:[(0,a.jsx)(S.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:s.performanceNote})]}),(0,a.jsxs)(k,{className:"max-w-2xl mx-auto bg-yellow-500/10 border-yellow-500/20",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsxs)(C,{className:"text-yellow-200",children:[(0,a.jsxs)("strong",{children:[s.warningTitle,":"]})," ",s.warningText]})]})]})})}),(0,a.jsx)("section",{className:"py-16",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)(j,{defaultValue:"features",className:"max-w-6xl mx-auto",children:[(0,a.jsxs)(N,{className:"grid w-full grid-cols-3 bg-black/20 border border-blue-500/20",children:[(0,a.jsxs)(y,{value:"features",className:"data-[state=active]:bg-blue-600",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2"}),s.features]}),(0,a.jsxs)(y,{value:"community",className:"data-[state=active]:bg-blue-600",children:[(0,a.jsx)(H.A,{className:"w-4 h-4 mr-2"}),s.community]}),(0,a.jsxs)(y,{value:"appeals",className:"data-[state=active]:bg-blue-600",children:[(0,a.jsx)(W.A,{className:"w-4 h-4 mr-2"}),s.appeals]})]}),(0,a.jsx)(w,{value:"features",className:"mt-8",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.featuresData.map((e,t)=>(0,a.jsxs)(x,{className:"bg-black/20 border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10",children:[(0,a.jsx)(u,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"w-5 h-5 text-white"})}),(0,a.jsx)(p,{className:"text-white text-lg",children:e.name})]})}),(0,a.jsx)(h,{children:(0,a.jsx)(g,{className:"text-gray-300",children:e.desc})})]},t))})}),(0,a.jsx)(w,{value:"community",className:"mt-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto",children:[(0,a.jsxs)(x,{className:"bg-black/20 border-blue-500/20",children:[(0,a.jsx)(u,{children:(0,a.jsxs)(p,{className:"text-white flex items-center",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 mr-2 text-green-500"}),s.qqGroup]})}),(0,a.jsxs)(h,{children:[(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"zh"===e?"加入我们的QQ群获取支持和交流":"Join our QQ group for support and discussion"}),(0,a.jsx)(v,{variant:"secondary",className:"text-lg px-4 py-2 bg-green-500/20 text-green-300 border-green-500/20",children:"1007642619"})]})]}),(0,a.jsxs)(x,{className:"bg-black/20 border-blue-500/20",children:[(0,a.jsx)(u,{children:(0,a.jsxs)(p,{className:"text-white flex items-center",children:[(0,a.jsx)(X.A,{className:"w-5 h-5 mr-2 text-purple-500"}),"Discord"]})}),(0,a.jsxs)(h,{children:[(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"zh"===e?"加入Discord服务器进行国际交流":"Join our Discord server for international communication"}),(0,a.jsx)(m,{asChild:!0,variant:"outline",className:"border-purple-500/20 text-purple-300 hover:bg-purple-500/10",children:(0,a.jsxs)("a",{href:"https://discord.gg/PWRXKE6aNM",target:"_blank",rel:"noopener noreferrer",children:["zh"===e?"加入Discord":"Join Discord",(0,a.jsx)(M.A,{className:"w-4 h-4 ml-2"})]})})]})]})]})}),(0,a.jsx)(w,{value:"appeals",className:"mt-8",children:(0,a.jsxs)(x,{className:"bg-red-500/10 border-red-500/20 max-w-2xl mx-auto",children:[(0,a.jsxs)(u,{children:[(0,a.jsxs)(p,{className:"text-white flex items-center",children:[(0,a.jsx)(W.A,{className:"w-5 h-5 mr-2 text-red-400"}),s.appeals]}),(0,a.jsx)(g,{className:"text-red-200",children:"zh"===e?"如果您认为被误封，请通过以下方式联系我们":"If you believe you were falsely banned, please contact us through the following methods"})]}),(0,a.jsxs)(h,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-black/20 p-4 rounded-lg border border-red-500/20",children:[(0,a.jsx)("h4",{className:"text-white font-semibold mb-2",children:s.appealContact}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(X.A,{className:"w-4 h-4 text-blue-400"}),(0,a.jsx)("span",{className:"text-gray-300",children:"QQ: "}),(0,a.jsx)(v,{variant:"secondary",className:"bg-blue-500/20 text-blue-300 border-blue-500/20",children:"3298837494"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(X.A,{className:"w-4 h-4 text-purple-400"}),(0,a.jsx)("span",{className:"text-gray-300",children:"Discord: "}),(0,a.jsx)(m,{asChild:!0,variant:"link",className:"p-0 h-auto text-purple-300 hover:text-purple-200",children:(0,a.jsx)("a",{href:"https://discord.gg/PWRXKE6aNM",target:"_blank",rel:"noopener noreferrer",children:"PWRXKE6aNM"})})]})]})]}),(0,a.jsxs)(k,{className:"bg-yellow-500/10 border-yellow-500/20",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)(C,{className:"text-yellow-200",children:"zh"===e?"请通过以上任意一种方式进行申诉。":"Please appeal through any of the above methods."})]})]})]})})]})})}),(0,a.jsx)("footer",{className:"border-t border-blue-500/20 bg-black/20 backdrop-blur-sm py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,a.jsx)("img",{src:"/logo.png",alt:"SAC Logo",className:"w-6 h-6 rounded-lg object-cover",onError:e=>{e.currentTarget.style.display="none",e.currentTarget.nextElementSibling.style.display="flex"}}),(0,a.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg items-center justify-center hidden",children:(0,a.jsx)(R.A,{className:"w-6 h-6 text-blue-400"})}),(0,a.jsx)("span",{className:"text-white font-semibold",children:"SimpleAntiCheat (SAC)"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"zh"===e?"\xa9 2025 Web Developed by kldhsh123。":"\xa9 2025 Web Developed by kldhsh123."})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[248,476,571,358],()=>t(5851)),_N_E=e.O()}]);