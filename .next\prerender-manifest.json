{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "4f5192b2b135bf6b28f471f36f8c4d22", "previewModeSigningKey": "b18fdf9f58c579b4fd7e4e7b26da1125c7d7f5e5a7bc69a8560b0f546a1ca094", "previewModeEncryptionKey": "cef4b9215a3a994b660f8fbefded7d2cbf86ef964940908dccf9b84df98e3e60"}}