{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "f5b1b677fa649adb219fdb9a8f4572c5", "previewModeSigningKey": "bdc3dfc92bdb00e6e875011744fdbf4dd69dc6a434fd253cb37a1d7e473d6ff4", "previewModeEncryptionKey": "9fd5d5133db9ee77409e3a01215b41183320fa75221e72d3d9c079449d2b1fb7"}}