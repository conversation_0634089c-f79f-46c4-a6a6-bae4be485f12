{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "1f0ef08390d78f22e46485f3c113cf36", "previewModeSigningKey": "8b24f640f5b06d5b9bae984a3ecf375459b691240218482384d477304c7c2b07", "previewModeEncryptionKey": "d627130ad578ea83e457d2fe86b0fe6bb9bffce9ebf46fd557db3360d198d96c"}}