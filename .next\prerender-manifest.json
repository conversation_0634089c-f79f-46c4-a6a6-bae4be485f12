{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "bf66549e9399f3b639b943519dd199b2", "previewModeSigningKey": "b808b368bf420a91035c4295aaf2da98e8d2f3bb97295de505010e6a99de3d40", "previewModeEncryptionKey": "c61207d2bb4586a910c2050ace14b7c42d7685542e3ddce8e70ae0366df5bd45"}}