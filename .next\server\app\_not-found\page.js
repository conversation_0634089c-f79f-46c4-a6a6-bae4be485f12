(()=>{var e={};e.id=492,e.ids=[492],e.modules={824:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2205,23)),Promise.resolve().then(t.t.bind(t,3757,23)),Promise.resolve().then(t.t.bind(t,1861,23)),Promise.resolve().then(t.t.bind(t,4260,23)),Promise.resolve().then(t.t.bind(t,2508,23)),Promise.resolve().then(t.t.bind(t,9792,23)),Promise.resolve().then(t.t.bind(t,526,23)),Promise.resolve().then(t.t.bind(t,1480,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2197:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>h,tree:()=>l});var n=t(5466),o=t(6525),s=t(1861),i=t.n(s),d=t(1546),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,907,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,7006)),"F:\\Desktop\\本地项目\\sac-anticheat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,907,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,5958,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,6463,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],u={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2879:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4376:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,3031,23)),Promise.resolve().then(t.t.bind(t,7019,23)),Promise.resolve().then(t.t.bind(t,8335,23)),Promise.resolve().then(t.t.bind(t,6926,23)),Promise.resolve().then(t.t.bind(t,698,23)),Promise.resolve().then(t.t.bind(t,1182,23)),Promise.resolve().then(t.t.bind(t,5020,23)),Promise.resolve().then(t.t.bind(t,7646,23))},6391:()=>{},7006:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>a});var n=t(5322),o=t(9227),s=t.n(o),i=t(3603),d=t.n(i);t(6391);let a={title:"SimpleAntiCheat (SAC)",description:"SimpleAntiCheat (SAC)",generator:"kldhsh.top",icons:{icon:"/ico.ico"}};function l({children:e}){return(0,n.jsxs)("html",{lang:"en",children:[(0,n.jsx)("head",{children:(0,n.jsx)("style",{children:`
html {
  font-family: ${s().style.fontFamily};
  --font-sans: ${s().variable};
  --font-mono: ${d().variable};
}
        `})}),(0,n.jsx)("body",{children:e})]})}},7303:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[337],()=>t(2197));module.exports=n})();